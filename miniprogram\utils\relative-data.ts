// 亲戚关系数据
export interface RelativeItem {
  relation: string;
  gender: 'male' | 'female';
}

export interface RelativeResult {
  title: string;
  description: string;
  otherNames: string[];
  relationship: string;
}

// 快捷关系按钮配置
export const quickRelations = [
  { relation: '爸爸', gender: 'male' as const, icon: '👨' },
  { relation: '妈妈', gender: 'female' as const, icon: '👩' },
  { relation: '哥哥', gender: 'male' as const, icon: '👦' },
  { relation: '姐姐', gender: 'female' as const, icon: '👧' },
  { relation: '弟弟', gender: 'male' as const, icon: '👦' },
  { relation: '妹妹', gender: 'female' as const, icon: '👧' },
  { relation: '儿子', gender: 'male' as const, icon: '👶' },
  { relation: '女儿', gender: 'female' as const, icon: '👶' },
  { relation: '老公', gender: 'male' as const, icon: '👨' },
  { relation: '老婆', gender: 'female' as const, icon: '👩' },
  { relation: '爷爷', gender: 'male' as const, icon: '👴' },
  { relation: '奶奶', gender: 'female' as const, icon: '👵' }
];

// 关系计算规则（简化版）
const relationshipRules: { [key: string]: RelativeResult } = {
  // 直接关系
  '爸爸': {
    title: '爸爸',
    description: '直接关系：父亲',
    otherNames: ['父亲', '爹', '老爸'],
    relationship: 'father'
  },
  '妈妈': {
    title: '妈妈',
    description: '直接关系：母亲',
    otherNames: ['母亲', '娘', '老妈'],
    relationship: 'mother'
  },
  
  // 父系关系
  '爸爸-哥哥': {
    title: '伯伯',
    description: '你爸爸的哥哥，应该称呼为"伯伯"或"大伯"',
    otherNames: ['大伯', '伯父'],
    relationship: 'father-brother-elder'
  },
  '爸爸-弟弟': {
    title: '叔叔',
    description: '你爸爸的弟弟，应该称呼为"叔叔"',
    otherNames: ['叔父'],
    relationship: 'father-brother-younger'
  },
  '爸爸-姐姐': {
    title: '姑妈',
    description: '你爸爸的姐姐，应该称呼为"姑妈"或"姑姑"',
    otherNames: ['姑姑', '姑母', '姑娘'],
    relationship: 'father-sister-elder'
  },
  '爸爸-妹妹': {
    title: '姑妈',
    description: '你爸爸的妹妹，应该称呼为"姑妈"或"姑姑"',
    otherNames: ['姑姑', '姑母'],
    relationship: 'father-sister-younger'
  },
  
  // 母系关系
  '妈妈-哥哥': {
    title: '舅舅',
    description: '你妈妈的哥哥，应该称呼为"舅舅"',
    otherNames: ['舅父'],
    relationship: 'mother-brother-elder'
  },
  '妈妈-弟弟': {
    title: '舅舅',
    description: '你妈妈的弟弟，应该称呼为"舅舅"',
    otherNames: ['舅父'],
    relationship: 'mother-brother-younger'
  },
  '妈妈-姐姐': {
    title: '姨妈',
    description: '你妈妈的姐姐，应该称呼为"姨妈"或"姨姨"',
    otherNames: ['姨姨', '姨母'],
    relationship: 'mother-sister-elder'
  },
  '妈妈-妹妹': {
    title: '姨妈',
    description: '你妈妈的妹妹，应该称呼为"姨妈"或"姨姨"',
    otherNames: ['姨姨', '姨母'],
    relationship: 'mother-sister-younger'
  },
  
  // 祖父母关系
  '爷爷-哥哥': {
    title: '伯祖父',
    description: '你爷爷的哥哥，应该称呼为"伯祖父"',
    otherNames: ['大爷爷'],
    relationship: 'grandfather-brother-elder'
  },
  '奶奶-姐姐': {
    title: '姑奶奶',
    description: '你奶奶的姐姐，应该称呼为"姑奶奶"',
    otherNames: ['姑祖母'],
    relationship: 'grandmother-sister-elder'
  },
  
  // 配偶关系
  '老公': {
    title: '老公',
    description: '直接关系：丈夫',
    otherNames: ['丈夫', '先生', '老伴'],
    relationship: 'husband'
  },
  '老婆': {
    title: '老婆',
    description: '直接关系：妻子',
    otherNames: ['妻子', '太太', '老伴'],
    relationship: 'wife'
  },
  
  // 子女关系
  '儿子': {
    title: '儿子',
    description: '直接关系：儿子',
    otherNames: ['孩子', '小子'],
    relationship: 'son'
  },
  '女儿': {
    title: '女儿',
    description: '直接关系：女儿',
    otherNames: ['孩子', '闺女'],
    relationship: 'daughter'
  }
};

// 计算亲戚关系
export function calculateRelationship(relationChain: RelativeItem[]): RelativeResult {
  if (relationChain.length === 0) {
    return {
      title: '未知关系',
      description: '请添加关系链',
      otherNames: [],
      relationship: 'unknown'
    };
  }
  
  if (relationChain.length === 1) {
    const key = relationChain[0].relation;
    return relationshipRules[key] || {
      title: key,
      description: `直接关系：${key}`,
      otherNames: [],
      relationship: 'direct'
    };
  }
  
  if (relationChain.length === 2) {
    const key = `${relationChain[0].relation}-${relationChain[1].relation}`;
    return relationshipRules[key] || {
      title: `${relationChain[0].relation}的${relationChain[1].relation}`,
      description: `通过${relationChain[0].relation}的关系链计算得出`,
      otherNames: [],
      relationship: 'complex'
    };
  }
  
  // 复杂关系（3个或以上）
  const relationPath = relationChain.map(item => item.relation).join('的');
  return {
    title: '复杂关系',
    description: `关系链：${relationPath}`,
    otherNames: ['关系较远'],
    relationship: 'distant'
  };
}

// 生成关系图谱数据
export function generateFamilyTree(relationChain: RelativeItem[]): any {
  // 简化的家族树结构
  const tree = {
    levels: [
      { name: '祖辈', members: ['爷爷奶奶'] },
      { name: '父辈', members: [] as string[] },
      { name: '同辈', members: ['我'] },
      { name: '子辈', members: [] as string[] }
    ]
  };
  
  // 根据关系链填充家族树
  relationChain.forEach((item, index) => {
    if (index === 0) {
      if (['爸爸', '妈妈'].includes(item.relation)) {
        tree.levels[1].members.push(item.relation);
      } else if (['爷爷', '奶奶'].includes(item.relation)) {
        tree.levels[0].members = [item.relation];
      }
    } else if (index === 1) {
      if (['哥哥', '姐姐', '弟弟', '妹妹'].includes(item.relation)) {
        tree.levels[1].members.push(`${relationChain[0].relation}的${item.relation}`);
      }
    }
  });
  
  return tree;
}
