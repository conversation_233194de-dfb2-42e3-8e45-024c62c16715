// 工具配置文件
import { getEnabledBanners, BannerConfig } from './banner-config';

interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  iconBg: string;
  path: string;
  disabled?: boolean;
}

interface Category {
  id: string;
  name: string;
}

interface FeaturedTool {
  id: string;
  name: string;
  description: string;
  path: string;
}

interface BannerItem {
  id: string;
  name: string;
  description: string;
  path: string;
  background?: string;
  icon?: string;
}

export const toolsConfig = {
  // 工具分类
  categories: [
    { id: 'all', name: '全部' },
    { id: 'psychology', name: '心理测试' },
    { id: 'calculator', name: '计算工具' },
    { id: 'life', name: '生活助手' }
  ] as Category[],

  // Banner轮播配置
  banners: [
    {
      id: 'mbti',
      name: 'MBTI 性格测试',
      description: '发现真实的自己，了解性格特质与职业匹配',
      path: '/pages/mbti/mbti',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      icon: '🧠'
    },
    {
      id: 'work-calculator',
      name: '工作性价比计算器',
      description: '科学评估你的工作价值，优化职业选择',
      path: '/pages/work-calculator/work-calculator',
      background: 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)',
      icon: '💰'
    },
    {
      id: 'relative-calculator',
      name: '亲戚关系计算器',
      description: '快速计算复杂的亲戚关系称谓，过年必备神器',
      path: '/pages/relative-calculator/relative-calculator',
      background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
      icon: '👨‍👩‍👧‍👦'
    }
  ] as BannerItem[],

  // 推荐工具（保持向后兼容）
  featuredTool: {
    id: 'mbti',
    name: 'MBTI 性格测试',
    description: '发现真实的自己，了解性格特质与职业匹配',
    path: '/pages/mbti/mbti'
  } as FeaturedTool,

  // 工具列表
  tools: [
    {
      id: 'mbti',
      name: 'MBTI 性格测试',
      description: '专业的16型人格测试，了解你的性格类型',
      category: 'psychology',
      icon: '🧠',
      iconBg: 'linear-gradient(135deg, #ff6b6b, #ee5a24)',
      path: '/pages/mbti/mbti'
    },
    {
      id: 'work-calculator',
      name: '工作性价比计算器',
      description: '计算你的工作性价比，优化职业选择',
      category: 'calculator',
      icon: '💰',
      iconBg: 'linear-gradient(135deg, #4ecdc4, #44a08d)',
      path: '/pages/work-calculator/work-calculator'
    },
    {
      id: 'relative-calculator',
      name: '亲戚关系计算器',
      description: '快速计算复杂的亲戚关系称谓',
      category: 'calculator',
      icon: '👨‍👩‍👧‍👦',
      iconBg: 'linear-gradient(135deg, #a8edea, #fed6e3)',
      path: '/pages/relative-calculator/relative-calculator'
    },
    {
      id: 'more-tools',
      name: '更多工具',
      description: '敬请期待更多实用工具...',
      category: 'life',
      icon: '🔧',
      iconBg: 'linear-gradient(135deg, #ffeaa7, #fab1a0)',
      path: '',
      disabled: true
    }
  ] as Tool[]
};

// 根据分类筛选工具
export function getToolsByCategory(category: string): Tool[] {
  if (category === 'all') {
    return toolsConfig.tools;
  }
  return toolsConfig.tools.filter(tool => tool.category === category);
}

// 获取推荐工具
export function getFeaturedTool(): FeaturedTool {
  return toolsConfig.featuredTool;
}

// 获取分类列表
export function getCategories(): Category[] {
  return toolsConfig.categories;
}

// 获取Banner列表
export function getBanners(): BannerConfig[] {
  return getEnabledBanners();
}

// 获取配置文件中的Banner列表（保持向后兼容）
export function getConfigBanners(): BannerItem[] {
  return toolsConfig.banners;
}
