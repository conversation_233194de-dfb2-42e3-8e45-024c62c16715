/* relative-calculator.wxss */
.container {
  height: 100vh;
  background: #f5f5f5;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
  padding: 40rpx;
  text-align: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 输入区域样式 */
.input-section {
  padding: 32rpx;
}

.input-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e5e5;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
  display: block;
}

/* 关系链样式 */
.relation-chain {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16rpx;
  min-height: 80rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 4rpx dashed #dee2e6;
  margin-bottom: 32rpx;
}

.chain-placeholder {
  color: #999;
  font-size: 28rpx;
  width: 100%;
  text-align: center;
}

.relation-item {
  background: #e3f2fd;
  color: #1976d2;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.relation-icon {
  font-size: 32rpx;
}

.relation-text {
  font-size: 28rpx;
}

.remove-btn {
  color: #f44336;
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 8rpx;
  cursor: pointer;
}

.relation-arrow {
  color: #666;
  font-size: 32rpx;
  margin: 0 8rpx;
}

/* 快捷按钮样式 */
.quick-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.quick-btn {
  background: white;
  border: 4rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.quick-btn:active {
  border-color: #0052d9;
  background: #f0f7ff;
}

.quick-icon {
  font-size: 48rpx;
}

.quick-text {
  font-size: 28rpx;
  color: #333;
}

/* 自定义输入样式 */
.custom-input-section {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.custom-input {
  margin-bottom: 16rpx;
}

.gender-selector {
  display: flex;
  gap: 16rpx;
}

.gender-btn {
  flex: 1;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
}

/* 结果展示样式 */
.result-section {
  padding: 32rpx;
}

.result-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e5e5;
  text-align: center;
}

.result-header {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  display: block;
}

.result-title {
  font-size: 96rpx;
  font-weight: bold;
  color: #e91e63;
  margin-bottom: 32rpx;
}

.result-description {
  margin-bottom: 32rpx;
}

.result-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: block;
}

/* 家族树样式 */
.family-tree {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  text-align: center;
}

.tree-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  display: block;
}

.tree-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.tree-level {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.level-name {
  font-size: 24rpx;
  color: #999;
  font-weight: 600;
}

.level-members {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tree-node {
  background: white;
  border: 4rpx solid #e5e5e5;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: #333;
}

.tree-node.highlight {
  background: #e3f2fd;
  border-color: #1976d2;
  color: #1976d2;
  font-weight: 600;
}

/* 其他称谓样式 */
.other-names {
  background: #fff3e0;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
  text-align: left;
}

.other-names-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #f57c00;
  margin-bottom: 16rpx;
  display: block;
}

.other-names-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.other-name-item {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  display: block;
}

/* 结果操作按钮样式 */
.result-actions {
  display: flex;
  gap: 24rpx;
}

.result-action-btn {
  flex: 1;
}
