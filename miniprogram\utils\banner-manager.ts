// Banner管理工具
import { 
  BannerConfig, 
  bannerConfigs, 
  getEnabledBanners, 
  getBannerById, 
  addBanner, 
  updateBanner, 
  toggleBanner, 
  setBannerOrder,
  backgroundPresets,
  iconPresets
} from './banner-config';

// Banner管理类
export class BannerManager {
  
  // 获取所有Banner
  static getAllBanners(): BannerConfig[] {
    return bannerConfigs;
  }
  
  // 获取启用的Banner
  static getActiveBanners(): BannerConfig[] {
    return getEnabledBanners();
  }
  
  // 创建新Banner
  static createBanner(config: Omit<BannerConfig, 'id'>): string {
    const id = `banner_${Date.now()}`;
    const newBanner: BannerConfig = {
      id,
      ...config,
      enabled: config.enabled !== false,
      order: config.order || bannerConfigs.length + 1
    };
    
    addBanner(newBanner);
    return id;
  }
  
  // 编辑Banner
  static editBanner(id: string, updates: Partial<BannerConfig>): boolean {
    return updateBanner(id, updates);
  }
  
  // 删除Banner（实际上是禁用）
  static deleteBanner(id: string): boolean {
    return toggleBanner(id, false);
  }
  
  // 启用Banner
  static enableBanner(id: string): boolean {
    return toggleBanner(id, true);
  }
  
  // 禁用Banner
  static disableBanner(id: string): boolean {
    return toggleBanner(id, false);
  }
  
  // 重新排序Banner
  static reorderBanners(bannerIds: string[]): boolean {
    try {
      bannerIds.forEach((id, index) => {
        setBannerOrder(id, index + 1);
      });
      return true;
    } catch (error) {
      console.error('重新排序失败:', error);
      return false;
    }
  }
  
  // 获取背景预设
  static getBackgroundPresets(): string[] {
    return backgroundPresets;
  }
  
  // 获取图标预设
  static getIconPresets(): string[] {
    return iconPresets;
  }
  
  // 验证Banner配置
  static validateBanner(config: Partial<BannerConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!config.name || config.name.trim().length === 0) {
      errors.push('Banner名称不能为空');
    }
    
    if (!config.description || config.description.trim().length === 0) {
      errors.push('Banner描述不能为空');
    }
    
    if (config.name && config.name.length > 20) {
      errors.push('Banner名称不能超过20个字符');
    }
    
    if (config.description && config.description.length > 50) {
      errors.push('Banner描述不能超过50个字符');
    }
    
    if (config.path && config.path.length > 0 && !config.path.startsWith('/pages/')) {
      errors.push('页面路径格式不正确');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  // 导出Banner配置
  static exportConfig(): string {
    return JSON.stringify(bannerConfigs, null, 2);
  }
  
  // 导入Banner配置
  static importConfig(configJson: string): { success: boolean; message: string } {
    try {
      const configs = JSON.parse(configJson) as BannerConfig[];
      
      // 验证配置格式
      if (!Array.isArray(configs)) {
        return { success: false, message: '配置格式错误：应为数组格式' };
      }
      
      // 验证每个Banner配置
      for (const config of configs) {
        const validation = this.validateBanner(config);
        if (!validation.valid) {
          return { 
            success: false, 
            message: `Banner "${config.name}" 配置错误：${validation.errors.join(', ')}` 
          };
        }
      }
      
      // 清空现有配置并导入新配置
      bannerConfigs.length = 0;
      bannerConfigs.push(...configs);
      
      return { success: true, message: '配置导入成功' };
    } catch (error) {
      return { success: false, message: '配置解析失败：JSON格式错误' };
    }
  }
  
  // 重置为默认配置
  static resetToDefault(): void {
    bannerConfigs.length = 0;
    bannerConfigs.push(
      {
        id: 'mbti',
        name: 'MBTI 性格测试',
        description: '发现真实的自己，了解性格特质与职业匹配',
        path: '/pages/mbti/mbti',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        icon: '🧠',
        enabled: true,
        order: 1
      },
      {
        id: 'work-calculator',
        name: '工作性价比计算器',
        description: '科学评估你的工作价值，优化职业选择',
        path: '/pages/work-calculator/work-calculator',
        background: 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)',
        icon: '💰',
        enabled: true,
        order: 2
      },
      {
        id: 'relative-calculator',
        name: '亲戚关系计算器',
        description: '快速计算复杂的亲戚关系称谓，过年必备神器',
        path: '/pages/relative-calculator/relative-calculator',
        background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        icon: '👨‍👩‍👧‍👦',
        enabled: true,
        order: 3
      }
    );
  }
  
  // 获取Banner统计信息
  static getStats(): { total: number; enabled: number; disabled: number } {
    const total = bannerConfigs.length;
    const enabled = bannerConfigs.filter(b => b.enabled !== false).length;
    const disabled = total - enabled;
    
    return { total, enabled, disabled };
  }
}
