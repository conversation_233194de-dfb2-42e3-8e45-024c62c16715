/**app.wxss**/
/* 全局样式 - 参考原型设计 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f5f5f5;
  color: #333;
}

/* 统一卡片样式 */
.card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  margin: 16rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e5e5;
}

.card-small {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin: 8rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e5e5;
}

/* 统一按钮样式 */
.btn-primary {
  background: #0052d9 !important;
  color: white !important;
  border-radius: 12rpx !important;
}

.btn-secondary {
  background: white !important;
  color: #666 !important;
  border: 2rpx solid #e5e5e5 !important;
  border-radius: 12rpx !important;
}

/* 统一标题样式 */
.title-large {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.title-medium {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.title-small {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 统一文本样式 */
.text-primary {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.text-secondary {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.text-hint {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 统一间距 */
.margin-top-small { margin-top: 16rpx; }
.margin-top-medium { margin-top: 32rpx; }
.margin-top-large { margin-top: 48rpx; }

.margin-bottom-small { margin-bottom: 16rpx; }
.margin-bottom-medium { margin-bottom: 32rpx; }
.margin-bottom-large { margin-bottom: 48rpx; }

.padding-small { padding: 16rpx; }
.padding-medium { padding: 32rpx; }
.padding-large { padding: 48rpx; }

/* 统一布局 */
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 统一圆角 */
.radius-small { border-radius: 8rpx; }
.radius-medium { border-radius: 12rpx; }
.radius-large { border-radius: 24rpx; }
.radius-round { border-radius: 50%; }
