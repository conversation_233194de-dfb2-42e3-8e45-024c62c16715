// Banner配置文件
export interface BannerConfig {
  id: string;
  name: string;
  description: string;
  path: string;
  background?: string;
  icon?: string;
  enabled?: boolean;
  order?: number;
}

// Banner配置列表
export const bannerConfigs: BannerConfig[] = [
  {
    id: 'mbti',
    name: 'MBTI 性格测试',
    description: '发现真实的自己，了解性格特质与职业匹配',
    path: '/pages/mbti/mbti',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    icon: '🧠',
    enabled: true,
    order: 1
  },
  {
    id: 'work-calculator',
    name: '工作性价比计算器',
    description: '科学评估你的工作价值，优化职业选择',
    path: '/pages/work-calculator/work-calculator',
    background: 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)',
    icon: '💰',
    enabled: true,
    order: 2
  },
  {
    id: 'relative-calculator',
    name: '亲戚关系计算器',
    description: '快速计算复杂的亲戚关系称谓，过年必备神器',
    path: '/pages/relative-calculator/relative-calculator',
    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    icon: '👨‍👩‍👧‍👦',
    enabled: true,
    order: 3
  },
  {
    id: 'coming-soon',
    name: '更多工具即将上线',
    description: '敬请期待更多实用工具，让生活更便捷',
    path: '',
    background: 'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)',
    icon: '🚀',
    enabled: false,
    order: 4
  }
];

// 获取启用的Banner列表
export function getEnabledBanners(): BannerConfig[] {
  return bannerConfigs
    .filter(banner => banner.enabled !== false)
    .sort((a, b) => (a.order || 0) - (b.order || 0));
}

// 根据ID获取Banner
export function getBannerById(id: string): BannerConfig | undefined {
  return bannerConfigs.find(banner => banner.id === id);
}

// 添加新Banner
export function addBanner(banner: BannerConfig): void {
  bannerConfigs.push(banner);
}

// 更新Banner配置
export function updateBanner(id: string, updates: Partial<BannerConfig>): boolean {
  const index = bannerConfigs.findIndex(banner => banner.id === id);
  if (index !== -1) {
    bannerConfigs[index] = { ...bannerConfigs[index], ...updates };
    return true;
  }
  return false;
}

// 启用/禁用Banner
export function toggleBanner(id: string, enabled: boolean): boolean {
  return updateBanner(id, { enabled });
}

// 设置Banner顺序
export function setBannerOrder(id: string, order: number): boolean {
  return updateBanner(id, { order });
}

// 预设的背景渐变色
export const backgroundPresets = [
  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // 紫蓝
  'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)', // 青绿
  'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', // 青粉
  'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)', // 黄橙
  'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)', // 红橙
  'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)', // 蓝色
  'linear-gradient(135deg, #fd79a8 0%, #e84393 100%)', // 粉红
  'linear-gradient(135deg, #fdcb6e 0%, #e17055 100%)', // 橙色
  'linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%)', // 紫色
  'linear-gradient(135deg, #00b894 0%, #00cec9 100%)'  // 绿青
];

// 常用图标
export const iconPresets = [
  '🧠', '💰', '👨‍👩‍👧‍👦', '🚀', '🎯', '📊', '🔧', '📱', 
  '💡', '🎨', '📚', '🏆', '⚡', '🌟', '🎪', '🎭'
];
