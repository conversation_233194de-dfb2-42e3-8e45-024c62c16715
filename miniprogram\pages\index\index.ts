// index.ts
import { toolsConfig, getToolsByCategory, getFeaturedTool, getCategories, getBanners } from '../../utils/tools-config';

Component({
  data: {
    banners: [],
    currentBannerIndex: 0,
    featuredTool: {},
    categories: [],
    tools: [],
    filteredTools: [],
    currentCategory: 'all'
  },

  lifetimes: {
    attached() {
      this.initData();
    }
  },

  methods: {
    // 初始化数据
    initData() {
      const banners = getBanners();
      const featuredTool = getFeaturedTool();
      const categories = getCategories();
      const tools = getToolsByCategory('all');

      this.setData({
        banners,
        featuredTool,
        categories,
        tools,
        filteredTools: tools
      });
    },

    // 切换分类
    switchCategory(e: any) {
      const category = e.currentTarget.dataset.category;
      const filteredTools = getToolsByCategory(category);

      this.setData({
        currentCategory: category,
        filteredTools
      });
    },

    // Banner轮播变化
    onBannerChange(e: any) {
      this.setData({
        currentBannerIndex: e.detail.current
      });
    },

    // Banner指示器点击
    onBannerIndicatorTap(e: any) {
      const index = e.currentTarget.dataset.index;
      this.setData({
        currentBannerIndex: index
      });

      // 手动切换到指定Banner
      const query = this.createSelectorQuery();
      query.select('.banner-swiper').context((res) => {
        if (res.context) {
          res.context.swipeTo(index);
        }
      }).exec();
    },

    // 跳转到工具页面
    goToTool(e: any) {
      const path = e.currentTarget.dataset.path;
      if (!path) {
        wx.showToast({
          title: '敬请期待',
          icon: 'none'
        });
        return;
      }

      wx.navigateTo({
        url: path
      });
    }
  }
})
