import { WorkData, WorkResult, calculateWorkWorth, defaultWorkData } from '../../utils/work-calculator-config';

Component({
  data: {
    activeTab: 'input',
    workData: { ...defaultWorkData },
    result: {} as WorkResult,
    hasResult: false
  },

  lifetimes: {
    attached() {
      this.initData();
    }
  },

  methods: {
    // 初始化数据
    initData() {
      this.setData({
        workData: { ...defaultWorkData },
        result: {},
        hasResult: false,
        activeTab: 'input'
      });
    },

    // 标签页切换
    onTabChange(e: any) {
      const activeTab = e.detail.value;

      if (activeTab === 'result' && !this.data.hasResult) {
        wx.showToast({
          title: '请先计算性价比',
          icon: 'none'
        });
        return;
      }

      this.setData({ activeTab });
    },

    // 输入框变化
    onInputChange(e: any) {
      const field = e.currentTarget.dataset.field;
      let value: any = e.detail.value;

      // 对布尔值字段特殊处理
      if (field === 'hasShuttle' || field === 'hasCanteen') {
        value = !!value;
      }

      this.setData({
        [`workData.${field}`]: value
      });
    },

    // 计算性价比
    calculateWorth() {
      const { workData } = this.data;

      // 简单验证年薪
      if (!workData.salary || parseFloat(workData.salary) <= 0) {
        wx.showToast({
          title: '请输入有效的年薪',
          icon: 'none'
        });
        return;
      }

      // 计算结果
      const result = calculateWorkWorth(workData);

      this.setData({
        result,
        hasResult: true,
        activeTab: 'result'
      });

      // 这里可以添加更新圆形进度条的逻辑（如果需要）
    },

    // 重新计算
    recalculate() {
      this.setData({
        activeTab: 'input',
        hasResult: false,
        result: {}
      });
    }
  }
});
