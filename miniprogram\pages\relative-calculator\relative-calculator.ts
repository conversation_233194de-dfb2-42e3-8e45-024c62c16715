// relative-calculator.ts
import { 
  RelativeItem, 
  RelativeResult, 
  quickRelations, 
  calculateRelationship, 
  generateFamilyTree 
} from '../../utils/relative-data';

Component({
  data: {
    relationChain: [] as RelativeItem[],
    quickRelations,
    customRelation: '',
    selectedGender: null as 'male' | 'female' | null,
    showResult: false,
    result: {} as RelativeResult,
    familyTree: { levels: [] }
  },

  lifetimes: {
    attached() {
      this.initData();
    }
  },

  methods: {
    // 初始化数据
    initData() {
      this.setData({
        relationChain: [],
        showResult: false
      });
    },

    // 添加快捷关系
    addQuickRelation(e: any) {
      const { relation, gender } = e.currentTarget.dataset;
      
      const relationChain = [...this.data.relationChain];
      relationChain.push({ relation, gender });
      
      this.setData({ relationChain });
    },

    // 自定义输入变化
    onCustomInputChange(e: any) {
      this.setData({
        customRelation: e.detail.value
      });
    },

    // 选择性别
    selectGender(e: any) {
      const gender = e.currentTarget.dataset.gender;
      this.setData({
        selectedGender: gender
      });
    },

    // 添加自定义关系
    addCustomRelation() {
      const { customRelation, selectedGender } = this.data;
      
      if (!customRelation.trim()) {
        wx.showToast({
          title: '请输入关系名称',
          icon: 'none'
        });
        return;
      }
      
      if (!selectedGender) {
        wx.showToast({
          title: '请选择性别',
          icon: 'none'
        });
        return;
      }
      
      const relationChain = [...this.data.relationChain];
      relationChain.push({ 
        relation: customRelation.trim(), 
        gender: selectedGender 
      });
      
      this.setData({
        relationChain,
        customRelation: '',
        selectedGender: null
      });
    },

    // 移除关系
    removeRelation(e: any) {
      const index = e.currentTarget.dataset.index;
      const relationChain = [...this.data.relationChain];
      relationChain.splice(index, 1);
      
      this.setData({ relationChain });
    },

    // 清空关系链
    clearChain() {
      this.setData({
        relationChain: [],
        showResult: false
      });
    },

    // 计算关系
    calculateRelation() {
      const { relationChain } = this.data;
      
      if (relationChain.length === 0) {
        wx.showToast({
          title: '请先添加关系',
          icon: 'none'
        });
        return;
      }
      
      // 计算关系结果
      const result = calculateRelationship(relationChain);
      
      // 生成家族树
      const familyTree = generateFamilyTree(relationChain);
      
      this.setData({
        result,
        familyTree,
        showResult: true
      });
      
      // 滚动到结果区域
      setTimeout(() => {
        const query = this.createSelectorQuery();
        query.select('.result-section').boundingClientRect((rect) => {
          if (rect) {
            wx.pageScrollTo({
              scrollTop: rect.top,
              duration: 300
            });
          }
        }).exec();
      }, 100);
    },

    // 分享结果
    shareResult() {
      const { result } = this.data;
      
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
      
      wx.showToast({
        title: '可以截图分享哦',
        icon: 'none'
      });
    },

    // 重置计算器
    resetCalculator() {
      this.setData({
        relationChain: [],
        showResult: false,
        customRelation: '',
        selectedGender: null
      });
    }
  }
})
