<!--mbti.wxml-->
<scroll-view class="container" scroll-y>
  <!-- 头部进度区域 -->
  <view class="header">
    <view class="header-content">
      <text class="header-title">MBTI 性格测试</text>
      <text class="header-subtitle">发现真实的自己</text>
      <t-progress 
        wx:if="{{currentStep === 'test'}}"
        percentage="{{progress}}" 
        theme="primary" 
        class="progress-bar"
      />
      <text wx:if="{{currentStep === 'test'}}" class="progress-text">
        {{currentQuestionIndex + 1}} / {{totalQuestions}}
      </text>
    </view>
  </view>

  <!-- 介绍页面 -->
  <view wx:if="{{currentStep === 'intro'}}" class="intro-section">
    <view class="intro-card">
      <text class="intro-title">🧠 关于 MBTI 测试</text>
      
      <view class="intro-content">
        <view class="intro-item">
          <text class="intro-label">什么是 MBTI？</text>
          <text class="intro-desc">MBTI（Myers-Briggs Type Indicator）是基于荣格心理类型理论开发的人格测试工具，将人格分为16种类型。</text>
        </view>
        
        <view class="intro-item">
          <text class="intro-label">四个维度：</text>
          <view class="dimension-list">
            <text class="dimension-item">• E/I - 外向/内向：能量来源</text>
            <text class="dimension-item">• S/N - 感觉/直觉：信息收集方式</text>
            <text class="dimension-item">• T/F - 思考/情感：决策方式</text>
            <text class="dimension-item">• J/P - 判断/知觉：生活方式</text>
          </view>
        </view>
        
        <view class="intro-item">
          <text class="intro-label">测试说明：</text>
          <view class="instruction-list">
            <text class="instruction-item">• 共{{totalQuestions}}道题，每题选择最符合你的选项</text>
            <text class="instruction-item">• 请诚实作答，没有标准答案</text>
            <text class="instruction-item">• 测试时间约5-10分钟</text>
          </view>
        </view>
      </view>
      
      <t-button theme="primary" size="large" class="start-btn" bindtap="startTest">
        开始测试
      </t-button>
    </view>
  </view>

  <!-- 测试页面 -->
  <view wx:if="{{currentStep === 'test'}}" class="test-section">
    <view class="question-card">
      <text class="question-text">{{currentQuestion.question}}</text>
      
      <t-radio-group value="{{selectedOption}}" bindchange="onOptionChange" class="options-container">
        <t-radio 
          wx:for="{{currentQuestion.options}}" 
          wx:key="index"
          value="{{index}}"
          label="{{item.text}}"
          class="option-item"
        />
      </t-radio-group>
      
      <view class="button-container">
        <t-button 
          wx:if="{{currentQuestionIndex > 0}}"
          theme="default" 
          size="medium"
          bindtap="previousQuestion"
          class="nav-btn"
        >
          上一题
        </t-button>
        <t-button 
          theme="primary" 
          size="medium"
          bindtap="nextQuestion"
          disabled="{{selectedOption === null}}"
          class="nav-btn"
        >
          {{currentQuestionIndex === totalQuestions - 1 ? '完成测试' : '下一题'}}
        </t-button>
      </view>
    </view>
  </view>

  <!-- 结果页面 -->
  <view wx:if="{{currentStep === 'result'}}" class="result-section">
    <view class="result-card">
      <text class="result-title">🎉 测试完成</text>
      <text class="personality-type">{{result.type}}</text>
      <text class="personality-name">{{result.name}}</text>
      <text class="personality-desc">{{result.description}}</text>
      
      <view class="scores-section">
        <text class="scores-title">各维度得分：</text>
        <view class="score-item">
          <text class="score-label">外向 (E)</text>
          <t-progress percentage="{{dimensionScores.E}}" theme="primary" class="score-progress" />
          <text class="score-value">{{dimensionScores.E}}%</text>
        </view>
        <view class="score-item">
          <text class="score-label">感觉 (S)</text>
          <t-progress percentage="{{dimensionScores.S}}" theme="primary" class="score-progress" />
          <text class="score-value">{{dimensionScores.S}}%</text>
        </view>
        <view class="score-item">
          <text class="score-label">思考 (T)</text>
          <t-progress percentage="{{dimensionScores.T}}" theme="primary" class="score-progress" />
          <text class="score-value">{{dimensionScores.T}}%</text>
        </view>
        <view class="score-item">
          <text class="score-label">判断 (J)</text>
          <t-progress percentage="{{dimensionScores.J}}" theme="primary" class="score-progress" />
          <text class="score-value">{{dimensionScores.J}}%</text>
        </view>
      </view>
      
      <view class="careers-section">
        <text class="careers-title">💼 适合的职业：</text>
        <text class="careers-text">{{result.careers.join('、')}}</text>
      </view>
      
      <view class="action-buttons">
        <t-button theme="default" size="medium" bindtap="shareResult" class="action-btn">
          分享结果
        </t-button>
        <t-button theme="primary" size="medium" bindtap="restartTest" class="action-btn">
          重新测试
        </t-button>
      </view>
    </view>
  </view>
</scroll-view>
