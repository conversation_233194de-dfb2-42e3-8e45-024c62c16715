// 测试配置文件
import { toolsConfig } from './tools-config';
import { mbtiQuestions, calculateMBTIResult } from './mbti-questions';
import { calculateWorkWorth, defaultWorkData } from './work-calculator-config';
import { calculateRelationship } from './relative-data';

// 测试工具配置
export function testToolsConfig() {
  console.log('=== 测试工具配置 ===');
  console.log('分类数量:', toolsConfig.categories.length);
  console.log('工具数量:', toolsConfig.tools.length);
  console.log('推荐工具:', toolsConfig.featuredTool.name);
  
  toolsConfig.tools.forEach(tool => {
    console.log(`工具: ${tool.name} - ${tool.description}`);
  });
}

// 测试MBTI配置
export function testMBTIConfig() {
  console.log('=== 测试MBTI配置 ===');
  console.log('题目数量:', mbtiQuestions.length);
  
  // 测试计算结果
  const testAnswers = new Array(20).fill(0); // 全选第一个选项
  const result = calculateMBTIResult(testAnswers);
  console.log('测试结果:', result.type, result.name);
}

// 测试工作计算器配置
export function testWorkCalculatorConfig() {
  console.log('=== 测试工作计算器配置 ===');
  console.log('默认配置:', defaultWorkData);
  
  const result = calculateWorkWorth(defaultWorkData);
  console.log('计算结果:', result.totalScore, result.level);
}

// 测试亲戚关系配置
export function testRelativeConfig() {
  console.log('=== 测试亲戚关系配置 ===');
  
  const testChain = [
    { relation: '爸爸', gender: 'male' as const },
    { relation: '姐姐', gender: 'female' as const }
  ];
  
  const result = calculateRelationship(testChain);
  console.log('关系计算结果:', result.title, result.description);
}

// 运行所有测试
export function runAllTests() {
  testToolsConfig();
  testMBTIConfig();
  testWorkCalculatorConfig();
  testRelativeConfig();
  console.log('=== 所有测试完成 ===');
}
