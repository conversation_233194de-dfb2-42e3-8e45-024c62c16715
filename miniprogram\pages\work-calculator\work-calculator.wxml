<!-- 使用TDesign组件，界面设计风格与网页版保持一致，文字内容完全按照网页版 -->

<scroll-view class="container" scroll-y>
  <!-- 头部 -->
  <view class="header">
    <view class="header-content">
      <text class="header-title">💰 工作性价比计算器</text>
      <text class="header-subtitle">科学评估你的工作价值</text>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tab-container">
    <t-tabs value="{{activeTab}}" bindchange="onTabChange" class="tabs">
      <t-tab-panel label="信息输入" value="input" />
      <t-tab-panel label="计算结果" value="result" />
    </t-tabs>
  </view>

  <!-- 输入表单 -->
  <view wx:if="{{activeTab === 'input'}}" class="input-section">
    <view class="form-card">
      <!-- 年薪 -->
      <view class="input-group">
        <text class="input-label">年薪（税前）</text>
        <t-input 
          value="{{workData.salary}}"
          type="number"
          placeholder="请输入年薪"
          suffix="万元"
          bindchange="onInputChange"
          data-field="salary"
          class="input-field"
        />
      </view>

      <!-- 是否非中国薪资 -->
      <view class="input-group">
        <t-switch
          checked="{{workData.nonChinaSalary}}"
          bindchange="onInputChange"
          data-field="nonChinaSalary"
          label="非中国薪资"
        />
      </view>

      <!-- 每周工作天数 -->
      <view class="input-group">
        <text class="input-label">每周工作天数</text>
        <t-input 
          value="{{workData.workDaysPerWeek}}"
          type="number"
          placeholder="5"
          suffix="天"
          bindchange="onInputChange"
          data-field="workDaysPerWeek"
          class="input-field"
        />
      </view>

      <!-- 每周远程工作天数 -->
      <view class="input-group">
        <text class="input-label">每周远程工作天数</text>
        <t-input 
          value="{{workData.wfhDaysPerWeek}}"
          type="number"
          placeholder="0"
          suffix="天"
          bindchange="onInputChange"
          data-field="wfhDaysPerWeek"
          class="input-field"
        />
      </view>

      <!-- 年假 -->
      <view class="input-group">
        <text class="input-label">年假天数</text>
        <t-input 
          value="{{workData.annualLeave}}"
          type="number"
          placeholder="5"
          suffix="天"
          bindchange="onInputChange"
          data-field="annualLeave"
          class="input-field"
        />
      </view>

      <!-- 带薪病假 -->
      <view class="input-group">
        <text class="input-label">带薪病假天数</text>
        <t-input 
          value="{{workData.paidSickLeave}}"
          type="number"
          placeholder="3"
          suffix="天"
          bindchange="onInputChange"
          data-field="paidSickLeave"
          class="input-field"
        />
      </view>

      <!-- 法定节假日 -->
      <view class="input-group">
        <text class="input-label">法定节假日天数</text>
        <t-input 
          value="{{workData.publicHolidays}}"
          type="number"
          placeholder="13"
          suffix="天"
          bindchange="onInputChange"
          data-field="publicHolidays"
          class="input-field"
        />
      </view>

      <!-- 每日工作小时 -->
      <view class="input-group">
        <text class="input-label">每日工作小时</text>
        <t-input 
          value="{{workData.workHours}}"
          type="number"
          placeholder="10"
          suffix="小时"
          bindchange="onInputChange"
          data-field="workHours"
          class="input-field"
        />
      </view>

      <!-- 通勤小时 -->
      <view class="input-group">
        <text class="input-label">通勤小时</text>
        <t-input 
          value="{{workData.commuteHours}}"
          type="number"
          placeholder="2"
          suffix="小时"
          bindchange="onInputChange"
          data-field="commuteHours"
          class="input-field"
        />
      </view>

      <!-- 休息时间 -->
      <view class="input-group">
        <text class="input-label">休息时间</text>
        <t-input 
          value="{{workData.restTime}}"
          type="number"
          placeholder="2"
          suffix="小时"
          bindchange="onInputChange"
          data-field="restTime"
          class="input-field"
        />
      </view>

      <!-- 城市系数 -->
      <view class="input-group">
        <text class="input-label">城市系数</text>
        <t-input 
          value="{{workData.cityFactor}}"
          type="number"
          placeholder="1.0"
          bindchange="onInputChange"
          data-field="cityFactor"
          class="input-field"
        />
      </view>

      <!-- 工作环境系数 -->
      <view class="input-group">
        <text class="input-label">工作环境系数</text>
        <t-input 
          value="{{workData.workEnvironment}}"
          type="number"
          placeholder="1.0"
          bindchange="onInputChange"
          data-field="workEnvironment"
          class="input-field"
        />
      </view>

      <!-- 领导力系数 -->
      <view class="input-group">
        <text class="input-label">领导力系数</text>
        <t-input 
          value="{{workData.leadership}}"
          type="number"
          placeholder="1.0"
          bindchange="onInputChange"
          data-field="leadership"
          class="input-field"
        />
      </view>

      <!-- 团队合作系数 -->
      <view class="input-group">
        <text class="input-label">团队合作系数</text>
        <t-input 
          value="{{workData.teamwork}}"
          type="number"
          placeholder="1.0"
          bindchange="onInputChange"
          data-field="teamwork"
          class="input-field"
        />
      </view>

      <!-- 是否家乡 -->
      <view class="input-group">
        <t-switch
          checked="{{workData.homeTown === 'yes'}}"
          bindchange="onInputChange"
          data-field="homeTown"
          class="switch"
          bindtap="toggleHomeTown"
        />
        <text>是否家乡</text>
      </view>

      <!-- 学位类型 -->
      <view class="input-group">
        <text class="input-label">学位类型</text>
        <t-select
          value="{{workData.degreeType}}"
          options='[{"label":"专科及以下","value":"belowBachelor"},{"label":"本科","value":"bachelor"},{"label":"硕士","value":"masters"},{"label":"博士","value":"phd"}]'
          bindchange="onInputChange"
          data-field="degreeType"
        />
      </view>

      <!-- 学校类型 -->
      <view class="input-group">
        <text class="input-label">学校类型</text>
        <t-select
          value="{{workData.schoolType}}"
          options='[{"label":"二本三本","value":"secondTier"},{"label":"双非/QS100/USnews50","value":"firstTier"},{"label":"985/211/QS30/USnews20","value":"elite"}]'
          bindchange="onInputChange"
          data-field="schoolType"
        />
      </view>

      <!-- 本科背景类型 -->
      <view class="input-group" wx:if="{{workData.degreeType === 'masters'}}">
        <text class="input-label">本科背景类型</text>
        <t-select
          value="{{workData.bachelorType}}"
          options='[{"label":"二本三本","value":"secondTier"},{"label":"双非/QS100/USnews50","value":"firstTier"},{"label":"985/211/QS30/USnews20","value":"elite"}]'
          bindchange="onInputChange"
          data-field="bachelorType"
        />
      </view>

      <!-- 工作年限 -->
      <view class="input-group">
        <text class="input-label">工作年限</text>
        <t-select
          value="{{workData.workYears}}"
          options='[{"label":"应届生","value":"0"},{"label":"1-3年","value":"1"},{"label":"3-5年","value":"2"},{"label":"5-8年","value":"4"},{"label":"8-10年","value":"6"},{"label":"10-12年","value":"10"},{"label":"12年以上","value":"15"}]'
          bindchange="onInputChange"
          data-field="workYears"
        />
      </view>

      <!-- 工作稳定度 -->
      <view class="input-group">
        <text class="input-label">工作稳定度</text>
<t-radio-group
  value="{{workData.jobStability}}"
  bindchange="onInputChange"
  data-field="jobStability"
>
  <t-radio value="government" label="体制内" />
  <t-radio value="state" label="央/国企" />
  <t-radio value="foreign" label="外企" />
  <t-radio value="private" label="私企" />
  <t-radio value="dispatch" label="派遣社员" />
  <t-radio value="freelance" label="自由职业" />
</t-radio-group>
      </view>

      <!-- 班车 -->
      <view class="input-group">
        <t-switch
          checked="{{workData.hasShuttle}}"
          bindchange="onInputChange"
          data-field="hasShuttle"
          class="switch"
        />
        <text>班车</text>
      </view>

      <view class="input-group" wx:if="{{workData.hasShuttle}}">
<t-radio-group
  value="{{workData.shuttle}}"
  bindchange="onInputChange"
  data-field="shuttle"
>
  <t-radio value="1.0" label="无" />
  <t-radio value="0.9" label="不便" />
  <t-radio value="0.7" label="方便" />
  <t-radio value="0.5" label="直达" />
</t-radio-group>
      </view>

      <!-- 食堂 -->
      <view class="input-group">
        <t-switch
          checked="{{workData.hasCanteen}}"
          bindchange="onInputChange"
          data-field="hasCanteen"
          class="switch"
        />
        <text>食堂</text>
      </view>

      <view class="input-group" wx:if="{{workData.hasCanteen}}">
<t-radio-group
  value="{{workData.canteen}}"
  bindchange="onInputChange"
  data-field="canteen"
>
  <t-radio value="1.0" label="无" />
  <t-radio value="1.05" label="一般" />
  <t-radio value="1.1" label="好" />
  <t-radio value="1.15" label="极好" />
</t-radio-group>
      </view>

      <!-- 计算按钮 -->
      <t-button theme="primary" size="large" bindtap="calculateWorth" class="calculate-btn">
        计算性价比
      </t-button>
    </view>
  </view>

  <!-- 结果展示 -->
  <view wx:if="{{activeTab === 'result'}}" class="result-section">
    <view class="result-card">
      <!-- 综合评分 -->
      <view class="score-section">
        <view class="score-circle">
          <text class="score-text">{{result.value.toFixed(2)}}</text>
        </view>
        <text class="score-title">综合性价比评分</text>
        <text class="score-level">{{result.assessment}}</text>
      </view>

      <!-- 详细数据 -->
      <view class="metrics-section">
        <t-cell-group>
          <t-cell title="年工作天数" description="{{result.workDaysPerYear}} 天" />
          <t-cell title="平均日薪" description="¥{{result.dailySalary.toFixed(2)}}" />
        </t-cell-group>
      </view>
    </view>

    <!-- 重新计算按钮 -->
    <t-button theme="default" size="large" bindtap="recalculate" class="recalculate-btn">
      重新计算
    </t-button>
  </view>
</scroll-view>
