# Banner配置指南

## 概述
青工具小程序支持通过配置文件管理首页的Banner轮播，可以灵活地添加、编辑、排序和控制Banner的显示。

## 配置文件位置
- 主配置文件：`miniprogram/utils/banner-config.ts`
- 管理工具：`miniprogram/utils/banner-manager.ts`

## Banner配置结构

```typescript
interface BannerConfig {
  id: string;          // 唯一标识符
  name: string;        // Banner名称
  description: string; // Banner描述
  path: string;        // 跳转路径
  background?: string; // 背景渐变色
  icon?: string;       // 图标emoji
  enabled?: boolean;   // 是否启用
  order?: number;      // 显示顺序
}
```

## 基础配置示例

### 1. 添加新Banner

```typescript
// 在 banner-config.ts 中添加
{
  id: 'new-tool',
  name: '新工具',
  description: '这是一个新的实用工具',
  path: '/pages/new-tool/new-tool',
  background: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
  icon: '🎯',
  enabled: true,
  order: 4
}
```

### 2. 修改现有Banner

```typescript
// 修改MBTI测试Banner的描述
{
  id: 'mbti',
  name: 'MBTI 性格测试',
  description: '全新升级的性格测试，更准确的结果分析', // 修改描述
  path: '/pages/mbti/mbti',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  icon: '🧠',
  enabled: true,
  order: 1
}
```

### 3. 禁用Banner

```typescript
// 设置 enabled: false 来禁用Banner
{
  id: 'coming-soon',
  name: '更多工具即将上线',
  description: '敬请期待更多实用工具，让生活更便捷',
  path: '',
  background: 'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)',
  icon: '🚀',
  enabled: false, // 禁用此Banner
  order: 4
}
```

## 使用Banner管理器

### 1. 创建新Banner

```typescript
import { BannerManager } from '../utils/banner-manager';

// 创建新Banner
const newBannerId = BannerManager.createBanner({
  name: '健康计算器',
  description: '计算BMI、卡路里等健康指标',
  path: '/pages/health-calculator/health-calculator',
  background: 'linear-gradient(135deg, #00b894 0%, #00cec9 100%)',
  icon: '💪'
});
```

### 2. 编辑Banner

```typescript
// 编辑现有Banner
BannerManager.editBanner('mbti', {
  description: '发现真实的自己，专业的16型人格测试'
});
```

### 3. 管理Banner状态

```typescript
// 启用Banner
BannerManager.enableBanner('new-tool');

// 禁用Banner
BannerManager.disableBanner('coming-soon');

// 重新排序
BannerManager.reorderBanners(['mbti', 'work-calculator', 'relative-calculator']);
```

## 预设资源

### 背景渐变色预设

```typescript
const backgrounds = [
  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // 紫蓝
  'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)', // 青绿
  'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', // 青粉
  'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)', // 黄橙
  'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)', // 红橙
  'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)', // 蓝色
  'linear-gradient(135deg, #fd79a8 0%, #e84393 100%)', // 粉红
  'linear-gradient(135deg, #fdcb6e 0%, #e17055 100%)', // 橙色
  'linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%)', // 紫色
  'linear-gradient(135deg, #00b894 0%, #00cec9 100%)'  // 绿青
];
```

### 图标预设

```typescript
const icons = [
  '🧠', '💰', '👨‍👩‍👧‍👦', '🚀', '🎯', '📊', '🔧', '📱', 
  '💡', '🎨', '📚', '🏆', '⚡', '🌟', '🎪', '🎭'
];
```

## 最佳实践

### 1. 命名规范
- **ID**: 使用小写字母和连字符，如 `health-calculator`
- **名称**: 简洁明了，不超过20个字符
- **描述**: 突出功能特点，不超过50个字符

### 2. 视觉设计
- **背景色**: 使用预设的渐变色，保持视觉一致性
- **图标**: 选择与功能相关的emoji图标
- **顺序**: 按重要性和使用频率排序

### 3. 路径配置
- **有效路径**: 确保页面路径存在且可访问
- **空路径**: 对于未开发的功能，可设置空路径并禁用

### 4. 性能考虑
- **数量控制**: 建议Banner数量不超过5个
- **图片优化**: 如使用图片背景，注意文件大小

### 5. 指示器样式优化
- **无重影设计**: 完全禁用swiper原生指示器，使用自定义样式
- **现代化外观**: 毛玻璃效果背景，圆角胶囊形状
- **动态效果**: 激活状态为长条形，非激活为圆点
- **交互反馈**: 点击缩放效果，平滑过渡动画

## 配置验证

使用Banner管理器的验证功能：

```typescript
const validation = BannerManager.validateBanner({
  name: '测试工具',
  description: '这是一个测试工具的描述',
  path: '/pages/test/test'
});

if (!validation.valid) {
  console.log('配置错误:', validation.errors);
}
```

## 导入导出配置

### 导出配置

```typescript
const configJson = BannerManager.exportConfig();
console.log(configJson);
```

### 导入配置

```typescript
const result = BannerManager.importConfig(configJson);
if (result.success) {
  console.log('导入成功');
} else {
  console.log('导入失败:', result.message);
}
```

## 故障排除

### 常见问题

1. **Banner不显示**
   - 检查 `enabled` 是否为 `true`
   - 确认配置文件语法正确

2. **轮播不工作**
   - 确保有多个启用的Banner
   - 检查swiper组件配置

3. **指示器重影问题**
   - 已禁用swiper原生指示器
   - 使用自定义指示器样式
   - 确保z-index层级正确

4. **跳转失败**
   - 验证页面路径是否正确
   - 确认目标页面已在app.json中注册

5. **指示器点击无响应**
   - 检查事件绑定是否正确
   - 确认swiper context获取成功

### 调试方法

```typescript
// 获取Banner统计信息
const stats = BannerManager.getStats();
console.log('Banner统计:', stats);

// 获取当前启用的Banner
const activeBanners = BannerManager.getActiveBanners();
console.log('启用的Banner:', activeBanners);
```

## 总结

通过配置文件管理Banner，可以灵活地控制首页展示内容，提升用户体验。建议定期检查和更新Banner配置，确保内容的时效性和准确性。
