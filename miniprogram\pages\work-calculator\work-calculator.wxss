/* work-calculator.wxss */
/* 基础容器 */
.container {
  height: 100vh;
  background: #f5f5f5;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
  padding: 40rpx;
  text-align: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 标签页样式 */
.tab-container {
  background: white;
  padding: 0 32rpx;
}

.tabs {
  border-bottom: 1rpx solid #e5e5e5;
}

/* 输入表单样式 */
.input-section {
  padding: 32rpx;
}

.form-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e5e5;
}

.input-group {
  margin-bottom: 40rpx;
}

.input-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.input-field {
  width: 100%;
}

/* 计算按钮 */
.calculate-btn {
  width: 100%;
  margin-top: 32rpx;
}

/* 结果展示样式 */
.result-section {
  padding: 32rpx;
}

.result-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e5e5;
}

/* 评分圆环样式 */
.score-section {
  text-align: center;
  margin-bottom: 48rpx;
}

.score-circle {
  width: 240rpx;
  height: 240rpx;
  border-radius: 50%;
  background: conic-gradient(#4ecdc4 0deg 252deg, #e5e5e5 252deg 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
  position: relative;
}

.score-text {
  position: relative;
  z-index: 1;
  font-size: 48rpx;
  font-weight: bold;
  color: #4ecdc4;
}

.score-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.score-level {
  font-size: 28rpx;
  color: #666;
  display: block;
}

/* 指标数据样式 */
.metrics-section {
  margin-bottom: 32rpx;
}

/* 重新计算按钮 */
.recalculate-btn {
  width: 100%;
  margin-top: 24rpx;
}
