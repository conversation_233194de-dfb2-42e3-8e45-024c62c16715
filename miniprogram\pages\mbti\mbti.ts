// mbti.ts
import { mbtiQuestions, calculateMBTIResult, getDimensionScores } from '../../utils/mbti-questions';

Component({
  data: {
    currentStep: 'intro', // 'intro' | 'test' | 'result'
    currentQuestionIndex: 0,
    totalQuestions: mbtiQuestions.length,
    currentQuestion: {},
    selectedOption: null,
    answers: [] as number[],
    progress: 0,
    result: {},
    dimensionScores: {}
  },

  lifetimes: {
    attached() {
      this.initData();
    }
  },

  methods: {
    // 初始化数据
    initData() {
      this.setData({
        totalQuestions: mbtiQuestions.length,
        answers: new Array(mbtiQuestions.length).fill(-1)
      });
    },

    // 开始测试
    startTest() {
      this.setData({
        currentStep: 'test',
        currentQuestionIndex: 0,
        currentQuestion: mbtiQuestions[0],
        selectedOption: null
      });
      this.updateProgress();
    },

    // 选项改变
    onOptionChange(e: any) {
      const selectedOption = parseInt(e.detail.value);
      const answers = [...this.data.answers];
      answers[this.data.currentQuestionIndex] = selectedOption;
      
      this.setData({
        selectedOption,
        answers
      });
    },

    // 下一题
    nextQuestion() {
      if (this.data.selectedOption === null) {
        wx.showToast({
          title: '请选择一个选项',
          icon: 'none'
        });
        return;
      }

      const nextIndex = this.data.currentQuestionIndex + 1;
      
      if (nextIndex >= this.data.totalQuestions) {
        // 测试完成，计算结果
        this.calculateResult();
      } else {
        // 下一题
        const savedAnswer = this.data.answers[nextIndex];
        this.setData({
          currentQuestionIndex: nextIndex,
          currentQuestion: mbtiQuestions[nextIndex],
          selectedOption: savedAnswer >= 0 ? savedAnswer : null
        });
        this.updateProgress();
      }
    },

    // 上一题
    previousQuestion() {
      if (this.data.currentQuestionIndex > 0) {
        const prevIndex = this.data.currentQuestionIndex - 1;
        const savedAnswer = this.data.answers[prevIndex];
        
        this.setData({
          currentQuestionIndex: prevIndex,
          currentQuestion: mbtiQuestions[prevIndex],
          selectedOption: savedAnswer >= 0 ? savedAnswer : null
        });
        this.updateProgress();
      }
    },

    // 更新进度
    updateProgress() {
      const progress = Math.round(((this.data.currentQuestionIndex + 1) / this.data.totalQuestions) * 100);
      this.setData({ progress });
    },

    // 计算结果
    calculateResult() {
      const result = calculateMBTIResult(this.data.answers);
      const dimensionScores = getDimensionScores(this.data.answers);
      
      this.setData({
        currentStep: 'result',
        result,
        dimensionScores
      });
    },

    // 分享结果
    shareResult() {
      const { result } = this.data;
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
      
      wx.showToast({
        title: '可以截图分享哦',
        icon: 'none'
      });
    },

    // 重新测试
    restartTest() {
      this.setData({
        currentStep: 'intro',
        currentQuestionIndex: 0,
        selectedOption: null,
        answers: new Array(mbtiQuestions.length).fill(-1),
        progress: 0
      });
    }
  }
})
