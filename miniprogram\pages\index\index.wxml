<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <!-- Banner轮播区域 -->
  <view class="banner-container">
    <swiper
      class="banner-swiper"
      indicator-dots="{{false}}"
      autoplay="{{banners.length > 1}}"
      interval="4000"
      duration="500"
      circular="{{banners.length > 1}}"
      bindchange="onBannerChange"
    >
      <swiper-item wx:for="{{banners}}" wx:key="id" class="banner-item">
        <view class="banner-card" style="background: {{item.background}};" bindtap="goToTool" data-path="{{item.path}}">
          <view class="banner-content">
            <text class="banner-icon">{{item.icon}}</text>
            <text class="banner-title">{{item.name}}</text>
            <text class="banner-desc">{{item.description}}</text>
            <view class="banner-action">
              <text class="banner-btn-text">立即使用 →</text>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 自定义指示器 -->
    <view wx:if="{{banners.length > 1}}" class="banner-indicators">
      <view
        wx:for="{{banners}}"
        wx:key="id"
        class="indicator-dot {{currentBannerIndex === index ? 'active' : ''}}"
        bindtap="onBannerIndicatorTap"
        data-index="{{index}}"
      ></view>
    </view>
  </view>

  <!-- 工具分类导航 -->
  <scroll-view class="category-nav" scroll-x>
    <view class="category-container">
      <view
        wx:for="{{categories}}"
        wx:key="id"
        class="category-item {{currentCategory === item.id ? 'active' : ''}}"
        bindtap="switchCategory"
        data-category="{{item.id}}"
      >
        {{item.name}}
      </view>
    </view>
  </scroll-view>

  <!-- 工具列表 -->
  <view class="tools-container">
    <view
      wx:for="{{filteredTools}}"
      wx:key="id"
      class="tool-card {{item.disabled ? 'tool-disabled' : ''}}"
      bindtap="goToTool"
      data-path="{{item.path}}"
    >
      <view class="tool-content">
        <view class="tool-icon" style="background: {{item.iconBg}};">
          {{item.icon}}
        </view>
        <view class="tool-info">
          <text class="tool-name">{{item.name}}</text>
          <text class="tool-desc">{{item.description}}</text>
        </view>
        <view class="tool-arrow">→</view>
      </view>
    </view>
  </view>
</scroll-view>
