// 工作性价比计算器配置，扩展自网页版逻辑

export interface WorkData {
  salary: string;            // 年薪（字符串，万元）
  nonChinaSalary: boolean;   // 是否非中国薪资
  workDaysPerWeek: string;   // 每周工作天数
  wfhDaysPerWeek: string;    // 每周远程工作天数
  annualLeave: string;       // 年假天数
  paidSickLeave: string;     // 带薪病假天数
  publicHolidays: string;    // 法定节假日天数
  workHours: string;         // 每日工作小时
  commuteHours: string;      // 通勤小时数
  restTime: string;          // 休息时间
  cityFactor: string;        // 城市系数
  workEnvironment: string;   // 工作环境系数
  leadership: string;        // 领导力系数
  teamwork: string;          // 团队合作系数
  homeTown: string;          // 是否家乡
  degreeType: string;        // 学位类型
  schoolType: string;        // 学校类型
  bachelorType: string;      // 本科背景类型
  workYears: string;         // 工作年限
  shuttle: string;           // 班车系数
  canteen: string;           // 食堂系数
  jobStability: string;      // 工作稳定度
  education: string;         // 教育系数
  hasShuttle: boolean;       // 是否有班车
  hasCanteen: boolean;       // 是否有食堂
}

export interface WorkResult {
  value: number;             // 计算的工作价值
  workDaysPerYear: number;   // 年工作天数
  dailySalary: number;       // 平均日薪
  assessment: string;        // 评级文本
  assessmentColor: string;   // 评级颜色
}

const pppFactors: Record<string, number> = {
  'CN': 4.19,
  // 其他国家PPP因子可根据需要补充
};

export const defaultWorkData: WorkData = {
  salary: '',
  nonChinaSalary: false,
  workDaysPerWeek: '5',
  wfhDaysPerWeek: '0',
  annualLeave: '5',
  paidSickLeave: '3',
  publicHolidays: '13',
  workHours: '10',
  commuteHours: '2',
  restTime: '2',
  cityFactor: '1.0',
  workEnvironment: '1.0',
  leadership: '1.0',
  teamwork: '1.0',
  homeTown: 'no',
  degreeType: 'bachelor',
  schoolType: 'firstTier',
  bachelorType: 'firstTier',
  workYears: '0',
  shuttle: '1.0',
  canteen: '1.0',
  jobStability: 'private',
  education: '1.0',
  hasShuttle: false,
  hasCanteen: false,
};

export function calculateWorkWorth(data: WorkData): WorkResult {
  if (!data.salary) {
    return {
      value: 0,
      workDaysPerYear: 0,
      dailySalary: 0,
      assessment: '请输入年薪',
      assessmentColor: 'text-gray-500',
    };
  }

  const salaryNum = parseFloat(data.salary);
  const workDaysPerWeekNum = parseFloat(data.workDaysPerWeek);
  const wfhDaysPerWeekNum = parseFloat(data.wfhDaysPerWeek) || 0;
  const annualLeaveNum = parseFloat(data.annualLeave);
  const paidSickLeaveNum = parseFloat(data.paidSickLeave);
  const publicHolidaysNum = parseFloat(data.publicHolidays);
  const workHoursNum = parseFloat(data.workHours);
  const commuteHoursNum = parseFloat(data.commuteHours);
  const restTimeNum = parseFloat(data.restTime);
  const cityFactorNum = parseFloat(data.cityFactor);
  const workEnvironmentNum = parseFloat(data.workEnvironment);
  const leadershipNum = parseFloat(data.leadership);
  const teamworkNum = parseFloat(data.teamwork);
  const educationNum = parseFloat(data.education);
  const shuttleNum = data.hasShuttle ? parseFloat(data.shuttle) : 1.0;
  const canteenNum = data.hasCanteen ? parseFloat(data.canteen) : 1.0;

  // 计算年工作天数
  const weeksPerYear = 52;
  const totalWorkDays = weeksPerYear * workDaysPerWeekNum;
  const totalLeaves = annualLeaveNum + publicHolidaysNum + paidSickLeaveNum * 0.6;
  const workDaysPerYear = Math.max(totalWorkDays - totalLeaves, 0);

  // 计算日薪，考虑PPP因子
  const pppFactor = data.nonChinaSalary ? (pppFactors['CN'] || 4.19) : 4.19;
  const standardizedSalary = salaryNum * (4.19 / pppFactor);
  const dailySalary = standardizedSalary / workDaysPerYear;

  // 计算有效通勤时间
  const officeDaysRatio = workDaysPerWeekNum > 0 ? (workDaysPerWeekNum - wfhDaysPerWeekNum) / workDaysPerWeekNum : 0;
  const effectiveCommuteHours = commuteHoursNum * officeDaysRatio * shuttleNum;

  // 计算环境系数
  const environmentFactor = workEnvironmentNum * leadershipNum * teamworkNum * cityFactorNum * canteenNum;

  // 根据工作年限计算经验薪资倍数
  const workYearsNum = parseFloat(data.workYears);
  let experienceSalaryMultiplier = 1.0;

  if (workYearsNum === 0) {
    if (data.jobStability === 'government') {
      experienceSalaryMultiplier = 0.8;
    } else if (data.jobStability === 'state') {
      experienceSalaryMultiplier = 0.9;
    } else if (data.jobStability === 'foreign') {
      experienceSalaryMultiplier = 0.95;
    } else if (data.jobStability === 'private') {
      experienceSalaryMultiplier = 1.0;
    } else if (data.jobStability === 'dispatch' || data.jobStability === 'freelance') {
      experienceSalaryMultiplier = 1.1;
    }
  } else {
    let baseSalaryMultiplier = 1.0;
    if (workYearsNum === 1) baseSalaryMultiplier = 1.5;
    else if (workYearsNum <= 3) baseSalaryMultiplier = 2.2;
    else if (workYearsNum <= 5) baseSalaryMultiplier = 2.7;
    else if (workYearsNum <= 8) baseSalaryMultiplier = 3.2;
    else if (workYearsNum <= 10) baseSalaryMultiplier = 3.6;
    else baseSalaryMultiplier = 3.9;

    let salaryGrowthFactor = 1.0;
    if (data.jobStability === 'foreign') salaryGrowthFactor = 0.8;
    else if (data.jobStability === 'state') salaryGrowthFactor = 0.4;
    else if (data.jobStability === 'government') salaryGrowthFactor = 0.2;
    else if (data.jobStability === 'dispatch' || data.jobStability === 'freelance') salaryGrowthFactor = 1.2;

    experienceSalaryMultiplier = 1 + (baseSalaryMultiplier - 1) * salaryGrowthFactor;
  }

  // 计算工作价值
  const value = (dailySalary * environmentFactor) /
    (35 * (workHoursNum + effectiveCommuteHours - 0.5 * restTimeNum) * educationNum * experienceSalaryMultiplier);

  // 评级判断
  let assessment = '';
  let assessmentColor = '';
  if (value < 0.6) {
    assessment = '很差';
    assessmentColor = 'text-pink-800';
  } else if (value < 1.0) {
    assessment = '较差';
    assessmentColor = 'text-red-500';
  } else if (value <= 1.8) {
    assessment = '一般';
    assessmentColor = 'text-orange-500';
  } else if (value <= 2.5) {
    assessment = '良好';
    assessmentColor = 'text-blue-500';
  } else if (value <= 3.2) {
    assessment = '优秀';
    assessmentColor = 'text-green-500';
  } else if (value <= 4.0) {
    assessment = '极好';
    assessmentColor = 'text-purple-500';
  } else {
    assessment = '完美';
    assessmentColor = 'text-yellow-400';
  }

  return {
    value,
    workDaysPerYear,
    dailySalary,
    assessment,
    assessmentColor,
  };
}
