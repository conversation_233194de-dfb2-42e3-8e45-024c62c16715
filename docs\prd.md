# 产品需求文档（PRD）

## 一、项目概述
**目标**：打造一款集合多种实用工具的微信小程序，提供简便的操作与快速的功能体验，帮助用户进行个性化的测试、计算与分析。

---

- **技术要求**：
  - 全部使用前端能力完成，工具类型，工具，banner中的工具、展示的内容都在前端的文件中配置

## 二、功能模块

### 1. **首页（工具集展示）**

- **Banner**：页面顶部展示推荐工具的Banner，Banner可以是轮播图，或者一个固定推荐工具的展示位。
  - **功能**：
    - 展示当前推荐工具的名称、简短描述、和进入工具的快捷入口。
    - 可支持广告位或置顶功能。
  
- **工具列表**：
  - 列表展示不同类型的工具，并按照类型分组。用户可以通过顶部的导航快速跳转到对应工具分类。
  - 每个工具显示工具名称、工具描述，点击后进入对应工具的详情页面。

- **导航**：  
  - 提供工具类型的导航栏，支持垂直滑动或顶部横向滑动，快速定位到相应类别的工具。

### 2. **工具详情页**

每个工具都有对应的详情页面，展示工具的介绍、使用方式以及相关功能。

---

## 三、工具功能需求

### 工具1：**MBTI测试**

- **功能描述**：
  - 点击工具后进入工具页面，首先展示MBTI的简要介绍，简洁地讲解测试的背景、用途和注意事项。
  - 进入正式测试页面，用户完成一系列选择题，每题有4个选项，用户选择自己认为最合适的选项。
  - 测试完成后，系统根据用户的答案计算并生成MBTI报告，报告应包括：
    - 用户的MBTI类型。
    - 每个维度的得分及其解释。
    - 适合的职业类型推荐。
  - **报告导出**：可以生成报告并导出，支持保存或分享。

### 工具2：**工作性价比计算器**

- **功能描述**：
  - 进入页面后展示不同的维度：年工作天数、平均日薪、工作性价比等，用户可以选择并填写自己的信息。
  - 根据输入的工作信息，计算出年工作天数、平均日薪、和工作性价比。
  - 计算维度及权重参考[GitHub上的计算项目](https://github.com/zippland/worth-calculator)，需要与该项目进行适配或重构。
  - **报告查看**：用户可以点击查看工作性价比的详细分析报告，包括各项维度的评分与结论。
  - **结果保存**：用户可以保存自己的计算结果或分享。

- **技术要求**：
  - 计算公式和维度可动态调整,维护一个配置文件

### 工具3：**亲戚计算器**

- **功能描述**：
  - 用户输入不同的亲戚关系，通过选择或输入亲戚名称，系统自动生成其关系图谱。
  - 支持显示详细的亲戚关系，例如：姑妈、舅舅等，用户可输入不同的亲戚类型。
  - **计算展示**：输入亲戚关系后，计算结果展示该亲戚在家族中的具体位置，并生成关系链图。
  - 支持分享关系图和解释。

- **技术要求**：
  - 提供便捷的界面供用户输入不同的亲戚信息。
  - 计算结果需要支持图形化展示（亲戚关系图）。

---

## 四、设计要求

- **界面风格**：简洁、直观、易操作，符合微信小程序的交互习惯。
- **响应式设计**：适配不同尺寸的手机屏幕，保证在各类设备上均有流畅体验。
- **UI设计**：每个工具页面设计简洁，易于理解，减少过多的交互步骤。

---

## 五、开发与技术架构

- **前端**：使用微信小程序框架，使用TDesign组件。

---
