<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MBTI 性格测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://tdesign.tencent.com/miniprogram/dist/tdesign.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 44px 20px 20px;
            text-align: center;
        }
        
        .progress-bar {
            background: rgba(255, 255, 255, 0.3);
            height: 4px;
            border-radius: 2px;
            margin: 16px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            background: white;
            height: 100%;
            width: 25%;
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .question-card {
            background: white;
            margin: 16px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .option-button {
            background: white;
            border: 2px solid #e5e5e5;
            border-radius: 12px;
            padding: 16px;
            margin: 8px 0;
            text-align: left;
            width: 100%;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .option-button:hover {
            border-color: #0052d9;
            background: #f0f7ff;
        }
        
        .option-button.selected {
            border-color: #0052d9;
            background: #0052d9;
            color: white;
        }
        
        .intro-section {
            background: white;
            margin: 16px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .result-card {
            background: white;
            margin: 16px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .personality-type {
            font-size: 48px;
            font-weight: bold;
            color: #0052d9;
            margin: 16px 0;
        }
        
        .dimension-score {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .score-bar {
            background: #f0f0f0;
            height: 8px;
            border-radius: 4px;
            width: 120px;
            overflow: hidden;
        }
        
        .score-fill {
            background: #0052d9;
            height: 100%;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <h1 class="text-xl font-bold">MBTI 性格测试</h1>
        <p class="text-sm opacity-90 mt-2">发现真实的自己</p>
        <div class="progress-bar">
            <div class="progress-fill" id="progressBar"></div>
        </div>
        <div class="text-sm opacity-90">
            <span id="currentQuestion">1</span> / <span id="totalQuestions">20</span>
        </div>
    </div>
    
    <!-- 介绍页面 -->
    <div id="introPage">
        <div class="intro-section">
            <h2 class="text-lg font-bold mb-4 text-center">🧠 关于 MBTI 测试</h2>
            <div class="space-y-4 text-sm text-gray-700">
                <p><strong>什么是 MBTI？</strong></p>
                <p>MBTI（Myers-Briggs Type Indicator）是基于荣格心理类型理论开发的人格测试工具，将人格分为16种类型。</p>
                
                <p><strong>四个维度：</strong></p>
                <ul class="list-disc list-inside space-y-1 ml-4">
                    <li><strong>E/I</strong> - 外向/内向：能量来源</li>
                    <li><strong>S/N</strong> - 感觉/直觉：信息收集方式</li>
                    <li><strong>T/F</strong> - 思考/情感：决策方式</li>
                    <li><strong>J/P</strong> - 判断/知觉：生活方式</li>
                </ul>
                
                <p><strong>测试说明：</strong></p>
                <ul class="list-disc list-inside space-y-1 ml-4">
                    <li>共20道题，每题选择最符合你的选项</li>
                    <li>请诚实作答，没有标准答案</li>
                    <li>测试时间约5-10分钟</li>
                </ul>
            </div>
            
            <button onclick="startTest()" class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium mt-6">
                开始测试
            </button>
        </div>
    </div>
    
    <!-- 测试页面 -->
    <div id="testPage" style="display: none;">
        <div class="question-card">
            <h3 class="text-lg font-semibold mb-4" id="questionText">
                在社交聚会中，你更倾向于：
            </h3>
            
            <div id="optionsContainer">
                <button class="option-button" onclick="selectOption(0)">
                    主动与很多人交谈，享受热闹的氛围
                </button>
                <button class="option-button" onclick="selectOption(1)">
                    与少数几个熟悉的朋友深入交流
                </button>
                <button class="option-button" onclick="selectOption(2)">
                    观察他人的互动，适时参与对话
                </button>
                <button class="option-button" onclick="selectOption(3)">
                    尽早离开，更喜欢安静的环境
                </button>
            </div>
            
            <div class="flex justify-between mt-6">
                <button onclick="previousQuestion()" class="px-6 py-2 border border-gray-300 rounded-lg" id="prevBtn">
                    上一题
                </button>
                <button onclick="nextQuestion()" class="px-6 py-2 bg-blue-600 text-white rounded-lg" id="nextBtn">
                    下一题
                </button>
            </div>
        </div>
    </div>
    
    <!-- 结果页面 -->
    <div id="resultPage" style="display: none;">
        <div class="result-card">
            <h2 class="text-xl font-bold mb-4">🎉 测试完成</h2>
            <div class="personality-type">ENFP</div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">竞选者</h3>
            <p class="text-sm text-gray-600 mb-6">
                热情、创造性和社交型的自由精神，总能找到理由微笑。
            </p>
            
            <div class="text-left">
                <h4 class="font-semibold mb-3">各维度得分：</h4>
                
                <div class="dimension-score">
                    <span class="text-sm">外向 (E)</span>
                    <div class="score-bar">
                        <div class="score-fill" style="width: 75%"></div>
                    </div>
                    <span class="text-sm text-gray-600">75%</span>
                </div>
                
                <div class="dimension-score">
                    <span class="text-sm">直觉 (N)</span>
                    <div class="score-bar">
                        <div class="score-fill" style="width: 80%"></div>
                    </div>
                    <span class="text-sm text-gray-600">80%</span>
                </div>
                
                <div class="dimension-score">
                    <span class="text-sm">情感 (F)</span>
                    <div class="score-bar">
                        <div class="score-fill" style="width: 70%"></div>
                    </div>
                    <span class="text-sm text-gray-600">70%</span>
                </div>
                
                <div class="dimension-score">
                    <span class="text-sm">知觉 (P)</span>
                    <div class="score-bar">
                        <div class="score-fill" style="width: 85%"></div>
                    </div>
                    <span class="text-sm text-gray-600">85%</span>
                </div>
            </div>
            
            <div class="mt-6 p-4 bg-blue-50 rounded-lg text-left">
                <h4 class="font-semibold mb-2">💼 适合的职业：</h4>
                <p class="text-sm text-gray-700">
                    心理咨询师、市场营销、创意设计师、教师、记者、演员、企业家
                </p>
            </div>
            
            <div class="flex gap-3 mt-6">
                <button class="flex-1 py-2 border border-gray-300 rounded-lg text-sm">
                    分享结果
                </button>
                <button class="flex-1 py-2 bg-blue-600 text-white rounded-lg text-sm">
                    保存报告
                </button>
            </div>
        </div>
    </div>
    
    <script>
        let currentQuestionIndex = 0;
        let answers = [];
        
        function startTest() {
            document.getElementById('introPage').style.display = 'none';
            document.getElementById('testPage').style.display = 'block';
            updateProgress();
        }
        
        function selectOption(optionIndex) {
            // 移除所有选中状态
            document.querySelectorAll('.option-button').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            // 添加选中状态
            document.querySelectorAll('.option-button')[optionIndex].classList.add('selected');
            
            // 保存答案
            answers[currentQuestionIndex] = optionIndex;
        }
        
        function nextQuestion() {
            if (answers[currentQuestionIndex] === undefined) {
                alert('请选择一个选项');
                return;
            }
            
            currentQuestionIndex++;
            
            if (currentQuestionIndex >= 20) {
                showResult();
            } else {
                updateProgress();
                // 这里可以加载下一题的内容
            }
        }
        
        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--;
                updateProgress();
                // 这里可以加载上一题的内容
            }
        }
        
        function updateProgress() {
            const progress = ((currentQuestionIndex + 1) / 20) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('currentQuestion').textContent = currentQuestionIndex + 1;
            
            // 更新按钮状态
            document.getElementById('prevBtn').style.display = currentQuestionIndex === 0 ? 'none' : 'block';
        }
        
        function showResult() {
            document.getElementById('testPage').style.display = 'none';
            document.getElementById('resultPage').style.display = 'block';
        }
    </script>
</body>
</html>
