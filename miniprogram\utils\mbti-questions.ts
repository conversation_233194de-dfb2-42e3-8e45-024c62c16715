// MBTI测试题库
interface MBTIQuestion {
  id: number;
  question: string;
  options: {
    text: string;
    dimension: 'E' | 'I' | 'S' | 'N' | 'T' | 'F' | 'J' | 'P';
    score: number;
  }[];
}

interface MBTIResult {
  type: string;
  name: string;
  description: string;
  careers: string[];
  traits: string[];
}

export const mbtiQuestions: MBTIQuestion[] = [
  {
    id: 1,
    question: "在社交聚会中，你更倾向于：",
    options: [
      { text: "主动与很多人交谈，享受热闹的氛围", dimension: "E", score: 3 },
      { text: "与少数几个熟悉的朋友深入交流", dimension: "I", score: 2 },
      { text: "观察他人的互动，适时参与对话", dimension: "I", score: 1 },
      { text: "尽早离开，更喜欢安静的环境", dimension: "I", score: 3 }
    ]
  },
  {
    id: 2,
    question: "当面对新的挑战时，你通常：",
    options: [
      { text: "立即行动，边做边学", dimension: "E", score: 2 },
      { text: "先仔细思考和计划", dimension: "I", score: 2 },
      { text: "寻求他人的建议和支持", dimension: "E", score: 1 },
      { text: "独自分析所有可能性", dimension: "I", score: 3 }
    ]
  },
  {
    id: 3,
    question: "你更喜欢关注：",
    options: [
      { text: "具体的事实和细节", dimension: "S", score: 3 },
      { text: "整体的概念和可能性", dimension: "N", score: 3 },
      { text: "实际的应用和结果", dimension: "S", score: 2 },
      { text: "创新的想法和理论", dimension: "N", score: 2 }
    ]
  },
  {
    id: 4,
    question: "在学习新知识时，你偏好：",
    options: [
      { text: "通过实践和体验来学习", dimension: "S", score: 2 },
      { text: "通过阅读和思考来理解", dimension: "N", score: 2 },
      { text: "按部就班地学习基础知识", dimension: "S", score: 1 },
      { text: "跳跃式地探索感兴趣的部分", dimension: "N", score: 1 }
    ]
  },
  {
    id: 5,
    question: "做决定时，你更依赖：",
    options: [
      { text: "逻辑分析和客观事实", dimension: "T", score: 3 },
      { text: "个人价值观和感受", dimension: "F", score: 3 },
      { text: "公平和一致的原则", dimension: "T", score: 2 },
      { text: "对他人的影响和和谐", dimension: "F", score: 2 }
    ]
  },
  {
    id: 6,
    question: "在团队中，你更愿意：",
    options: [
      { text: "提供客观的分析和建议", dimension: "T", score: 2 },
      { text: "关注团队成员的感受", dimension: "F", score: 2 },
      { text: "确保任务高效完成", dimension: "T", score: 1 },
      { text: "维护团队的和谐氛围", dimension: "F", score: 1 }
    ]
  },
  {
    id: 7,
    question: "你的生活方式更倾向于：",
    options: [
      { text: "有计划、有条理", dimension: "J", score: 3 },
      { text: "灵活、随性", dimension: "P", score: 3 },
      { text: "按时完成任务", dimension: "J", score: 2 },
      { text: "保持开放的选择", dimension: "P", score: 2 }
    ]
  },
  {
    id: 8,
    question: "面对截止日期，你通常：",
    options: [
      { text: "提前完成，避免最后匆忙", dimension: "J", score: 2 },
      { text: "在压力下激发最佳表现", dimension: "P", score: 2 },
      { text: "制定详细的时间计划", dimension: "J", score: 1 },
      { text: "保持灵活，随时调整", dimension: "P", score: 1 }
    ]
  },
  {
    id: 9,
    question: "在解决问题时，你更倾向于：",
    options: [
      { text: "关注具体的事实和数据", dimension: "S", score: 3 },
      { text: "寻找创新的解决方案", dimension: "N", score: 3 },
      { text: "按照已有的经验处理", dimension: "S", score: 2 },
      { text: "探索多种可能性", dimension: "N", score: 2 }
    ]
  },
  {
    id: 10,
    question: "你更喜欢的工作环境是：",
    options: [
      { text: "热闹活跃，有很多互动", dimension: "E", score: 3 },
      { text: "安静专注，能独立思考", dimension: "I", score: 3 },
      { text: "有团队合作的机会", dimension: "E", score: 2 },
      { text: "能够深入研究的空间", dimension: "I", score: 2 }
    ]
  },
  {
    id: 11,
    question: "当朋友向你倾诉烦恼时，你会：",
    options: [
      { text: "分析问题，提供解决方案", dimension: "T", score: 3 },
      { text: "倾听理解，给予情感支持", dimension: "F", score: 3 },
      { text: "客观地指出问题所在", dimension: "T", score: 2 },
      { text: "安慰鼓励，陪伴左右", dimension: "F", score: 2 }
    ]
  },
  {
    id: 12,
    question: "你的理想假期是：",
    options: [
      { text: "详细规划每天的行程", dimension: "J", score: 3 },
      { text: "随心所欲，走到哪算哪", dimension: "P", score: 3 },
      { text: "有大致计划但保持弹性", dimension: "J", score: 1 },
      { text: "完全没有计划，自由自在", dimension: "P", score: 2 }
    ]
  },
  {
    id: 13,
    question: "在会议中，你通常：",
    options: [
      { text: "积极发言，分享想法", dimension: "E", score: 3 },
      { text: "仔细倾听，深思熟虑后发言", dimension: "I", score: 3 },
      { text: "主动参与讨论", dimension: "E", score: 2 },
      { text: "更愿意会后私下交流", dimension: "I", score: 2 }
    ]
  },
  {
    id: 14,
    question: "你更重视：",
    options: [
      { text: "实用性和可操作性", dimension: "S", score: 3 },
      { text: "创新性和可能性", dimension: "N", score: 3 },
      { text: "经过验证的方法", dimension: "S", score: 2 },
      { text: "未来的发展潜力", dimension: "N", score: 2 }
    ]
  },
  {
    id: 15,
    question: "做重要决定时，你会：",
    options: [
      { text: "理性分析利弊得失", dimension: "T", score: 3 },
      { text: "考虑对相关人员的影响", dimension: "F", score: 3 },
      { text: "基于客观标准判断", dimension: "T", score: 2 },
      { text: "听从内心的声音", dimension: "F", score: 2 }
    ]
  },
  {
    id: 16,
    question: "你的工作风格是：",
    options: [
      { text: "按计划有序推进", dimension: "J", score: 3 },
      { text: "灵活应变，适时调整", dimension: "P", score: 3 },
      { text: "喜欢明确的目标和期限", dimension: "J", score: 2 },
      { text: "保持多个选择开放", dimension: "P", score: 2 }
    ]
  },
  {
    id: 17,
    question: "在学习新技能时，你偏好：",
    options: [
      { text: "通过实践和练习掌握", dimension: "S", score: 3 },
      { text: "理解原理后举一反三", dimension: "N", score: 3 },
      { text: "按步骤循序渐进", dimension: "S", score: 2 },
      { text: "探索不同的学习方法", dimension: "N", score: 2 }
    ]
  },
  {
    id: 18,
    question: "你更容易被什么激励：",
    options: [
      { text: "成就感和认可", dimension: "E", score: 2 },
      { text: "个人成长和满足感", dimension: "I", score: 2 },
      { text: "团队的成功", dimension: "E", score: 1 },
      { text: "内在的价值实现", dimension: "I", score: 3 }
    ]
  },
  {
    id: 19,
    question: "面对批评时，你会：",
    options: [
      { text: "客观分析批评的合理性", dimension: "T", score: 3 },
      { text: "关注批评者的动机和感受", dimension: "F", score: 3 },
      { text: "专注于改进的具体方法", dimension: "T", score: 2 },
      { text: "考虑如何维护关系和谐", dimension: "F", score: 2 }
    ]
  },
  {
    id: 20,
    question: "你的生活节奏是：",
    options: [
      { text: "规律有序，按部就班", dimension: "J", score: 3 },
      { text: "随性自然，顺其自然", dimension: "P", score: 3 },
      { text: "大部分时间有规律", dimension: "J", score: 2 },
      { text: "经常有意外的变化", dimension: "P", score: 2 }
    ]
  }
];

// MBTI类型结果
export const mbtiResults: { [key: string]: MBTIResult } = {
  'ENFP': {
    type: 'ENFP',
    name: '竞选者',
    description: '热情、创造性和社交型的自由精神，总能找到理由微笑。',
    careers: ['心理咨询师', '市场营销', '创意设计师', '教师', '记者', '演员', '企业家'],
    traits: ['富有创造力', '热情洋溢', '善于沟通', '灵活适应']
  },
  'INFP': {
    type: 'INFP',
    name: '调停者',
    description: '诗人般的理想主义者，总是希望促成积极的变化。',
    careers: ['作家', '心理学家', '艺术家', '社会工作者', '翻译', '编辑'],
    traits: ['理想主义', '富有同情心', '创造性', '价值观驱动']
  },
  'ENTJ': {
    type: 'ENTJ',
    name: '指挥官',
    description: '大胆、富有想象力、意志强烈的领导者，总能找到或创造解决方法。',
    careers: ['CEO', '管理顾问', '律师', '投资银行家', '项目经理', '企业家'],
    traits: ['天生的领导者', '战略思维', '高效执行', '目标导向']
  },
  'INTJ': {
    type: 'INTJ',
    name: '建筑师',
    description: '富有想象力和战略性的思想家，一切皆在计划之中。',
    careers: ['科学家', '工程师', '建筑师', '系统分析师', '研究员', '战略规划师'],
    traits: ['独立思考', '战略规划', '系统性', '追求完美']
  },
  'ENTP': {
    type: 'ENTP',
    name: '辩论家',
    description: '聪明好奇的思想家，不会放过任何挑战。',
    careers: ['企业家', '发明家', '律师', '记者', '咨询师', '营销专家'],
    traits: ['创新思维', '善于辩论', '适应性强', '充满活力']
  },
  'INTP': {
    type: 'INTP',
    name: '逻辑学家',
    description: '具有创新精神的发明家，对知识有着不可抑制的渴望。',
    careers: ['研究员', '程序员', '数学家', '哲学家', '分析师', '理论家'],
    traits: ['逻辑思维', '独立自主', '求知欲强', '客观理性']
  },
  'ENFJ': {
    type: 'ENFJ',
    name: '主人公',
    description: '富有魅力和鼓舞力的领导者，能够令听众全神贯注。',
    careers: ['教师', '心理咨询师', '人力资源', '培训师', '社会工作者', '政治家'],
    traits: ['天生领导', '富有同情心', '善于激励', '关注他人']
  },
  'INFJ': {
    type: 'INFJ',
    name: '提倡者',
    description: '安静而神秘，同时鼓舞人心且不知疲倦的理想主义者。',
    careers: ['心理学家', '作家', '艺术家', '咨询师', '社会工作者', '宗教领袖'],
    traits: ['理想主义', '富有洞察力', '坚定信念', '关怀他人']
  },
  'ESTJ': {
    type: 'ESTJ',
    name: '总经理',
    description: '出色的管理者，在管理事务或人员方面表现卓越。',
    careers: ['管理者', '行政人员', '银行家', '会计师', '项目经理', '军官'],
    traits: ['组织能力强', '责任心强', '实用主义', '决策果断']
  },
  'ISTJ': {
    type: 'ISTJ',
    name: '物流师',
    description: '实用主义的逻辑学家，忠诚可靠，是社会的基石。',
    careers: ['会计师', '审计师', '银行家', '工程师', '法官', '管理员'],
    traits: ['可靠稳定', '注重细节', '有条不紊', '忠诚负责']
  },
  'ESFJ': {
    type: 'ESFJ',
    name: '执政官',
    description: '极有同情心、受欢迎的人，总是热心帮助他人。',
    careers: ['护士', '教师', '社会工作者', '人力资源', '销售代表', '客服专员'],
    traits: ['热心助人', '善于合作', '重视和谐', '关注他人需求']
  },
  'ISFJ': {
    type: 'ISFJ',
    name: '守卫者',
    description: '非常专注而温暖的守护者，时刻准备保护爱着的人们。',
    careers: ['护士', '教师', '图书管理员', '社会工作者', '心理咨询师', '医生'],
    traits: ['忠诚可靠', '细心体贴', '默默奉献', '保护他人']
  },
  'ESTP': {
    type: 'ESTP',
    name: '企业家',
    description: '聪明、精力充沛的感知者，真正享受生活在边缘的感觉。',
    careers: ['销售员', '企业家', '运动员', '演员', '警察', '急救人员'],
    traits: ['行动力强', '适应性强', '善于交际', '现实主义']
  },
  'ISTP': {
    type: 'ISTP',
    name: '鉴赏家',
    description: '大胆而实用的实验家，擅长使用各种工具。',
    careers: ['工程师', '技师', '飞行员', '外科医生', '侦探', '运动员'],
    traits: ['动手能力强', '冷静理性', '独立自主', '解决问题']
  },
  'ESFP': {
    type: 'ESFP',
    name: '娱乐家',
    description: '自发的、精力充沛的娱乐者，生活对他们来说从不无聊。',
    careers: ['演员', '音乐家', '销售员', '导游', '社会工作者', '儿童工作者'],
    traits: ['热情开朗', '善于表达', '关注当下', '富有创造力']
  },
  'ISFP': {
    type: 'ISFP',
    name: '冒险家',
    description: '灵活有魅力的艺术家，时刻准备探索新的可能性。',
    careers: ['艺术家', '设计师', '音乐家', '心理咨询师', '兽医', '摄影师'],
    traits: ['艺术天赋', '温和友善', '价值观驱动', '灵活适应']
  }
};

// 计算MBTI结果
export function calculateMBTIResult(answers: number[]): MBTIResult {
  const scores = { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 };
  
  answers.forEach((answerIndex, questionIndex) => {
    if (answerIndex >= 0 && questionIndex < mbtiQuestions.length) {
      const option = mbtiQuestions[questionIndex].options[answerIndex];
      scores[option.dimension] += option.score;
    }
  });

  const type = 
    (scores.E > scores.I ? 'E' : 'I') +
    (scores.S > scores.N ? 'S' : 'N') +
    (scores.T > scores.F ? 'T' : 'F') +
    (scores.J > scores.P ? 'J' : 'P');

  return mbtiResults[type] || mbtiResults['ENFP'];
}

// 获取维度得分百分比
export function getDimensionScores(answers: number[]): { [key: string]: number } {
  const scores = { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 };
  
  answers.forEach((answerIndex, questionIndex) => {
    if (answerIndex >= 0 && questionIndex < mbtiQuestions.length) {
      const option = mbtiQuestions[questionIndex].options[answerIndex];
      scores[option.dimension] += option.score;
    }
  });

  const totalE = scores.E + scores.I;
  const totalS = scores.S + scores.N;
  const totalT = scores.T + scores.F;
  const totalJ = scores.J + scores.P;

  return {
    E: totalE > 0 ? Math.round((scores.E / totalE) * 100) : 50,
    S: totalS > 0 ? Math.round((scores.S / totalS) * 100) : 50,
    T: totalT > 0 ? Math.round((scores.T / totalT) * 100) : 50,
    J: totalJ > 0 ? Math.round((scores.J / totalJ) * 100) : 50
  };
}
