// Banner轮播测试工具
import { getBanners } from './tools-config';
import { BannerManager } from './banner-manager';

// 测试Banner配置
export function testBannerConfig() {
  console.log('=== Banner配置测试 ===');
  
  // 获取Banner列表
  const banners = getBanners();
  console.log('Banner数量:', banners.length);
  
  banners.forEach((banner, index) => {
    console.log(`Banner ${index + 1}:`, {
      id: banner.id,
      name: banner.name,
      description: banner.description,
      enabled: banner.enabled,
      order: banner.order
    });
  });
  
  // 测试Banner管理器
  const stats = BannerManager.getStats();
  console.log('Banner统计:', stats);
  
  // 验证Banner配置
  banners.forEach(banner => {
    const validation = BannerManager.validateBanner(banner);
    if (!validation.valid) {
      console.error(`Banner "${banner.name}" 配置错误:`, validation.errors);
    } else {
      console.log(`Banner "${banner.name}" 配置正确`);
    }
  });
}

// 测试轮播功能
export function testSwiperFunction() {
  console.log('=== 轮播功能测试 ===');
  
  const banners = getBanners();
  
  if (banners.length <= 1) {
    console.log('Banner数量不足，轮播功能将被禁用');
    return false;
  }
  
  console.log('轮播功能已启用，Banner数量:', banners.length);
  console.log('自动轮播间隔: 4秒');
  console.log('支持手动滑动和指示器点击');
  
  return true;
}

// 模拟Banner切换
export function simulateBannerSwitch() {
  const banners = getBanners();
  let currentIndex = 0;
  
  console.log('=== 模拟Banner切换 ===');
  
  const switchBanner = () => {
    if (banners.length === 0) return;
    
    const banner = banners[currentIndex];
    console.log(`当前Banner: ${banner.name} (${currentIndex + 1}/${banners.length})`);
    
    currentIndex = (currentIndex + 1) % banners.length;
  };
  
  // 模拟切换5次
  for (let i = 0; i < 5; i++) {
    switchBanner();
  }
}

// 检查Banner路径有效性
export function checkBannerPaths() {
  console.log('=== Banner路径检查 ===');
  
  const banners = getBanners();
  const validPaths = [
    '/pages/index/index',
    '/pages/mbti/mbti',
    '/pages/work-calculator/work-calculator',
    '/pages/relative-calculator/relative-calculator'
  ];
  
  banners.forEach(banner => {
    if (!banner.path) {
      console.warn(`Banner "${banner.name}" 没有设置路径`);
    } else if (!validPaths.includes(banner.path)) {
      console.warn(`Banner "${banner.name}" 路径可能无效: ${banner.path}`);
    } else {
      console.log(`Banner "${banner.name}" 路径有效: ${banner.path}`);
    }
  });
}

// 运行所有测试
export function runAllBannerTests() {
  console.log('🚀 开始Banner功能测试...\n');
  
  testBannerConfig();
  console.log('\n');
  
  testSwiperFunction();
  console.log('\n');
  
  simulateBannerSwitch();
  console.log('\n');
  
  checkBannerPaths();
  console.log('\n');
  
  console.log('✅ Banner功能测试完成');
}
