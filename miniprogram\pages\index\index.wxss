/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
  background: #f5f5f5;
}

/* Banner轮播样式 */
.banner-container {
  position: relative;
  margin: 32rpx;
}

.banner-swiper {
  height: 320rpx;
  border-radius: 32rpx;
  overflow: hidden;
}

/* 隐藏swiper原生指示器 */
.banner-swiper .wx-swiper-dots {
  display: none !important;
}

.banner-swiper .wx-swiper-dot {
  display: none !important;
}

.banner-item {
  height: 100%;
}

.banner-card {
  height: 100%;
  border-radius: 32rpx;
  padding: 40rpx;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.banner-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  z-index: 2;
}

.banner-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  display: block;
}

.banner-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  display: block;
}

.banner-desc {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 24rpx;
  line-height: 1.4;
  flex: 1;
  display: block;
}

.banner-action {
  align-self: flex-start;
}

.banner-btn-text {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
}

/* 自定义指示器样式 */
.banner-indicators {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8rpx;
  z-index: 10;
  background: rgba(0, 0, 0, 0.15);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.indicator-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.indicator-dot.active {
  background: rgba(255, 255, 255, 0.9);
  width: 24rpx;
  border-radius: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.3);
}

/* 添加点击效果 */
.indicator-dot:active {
  transform: scale(0.9);
}

/* 分类导航样式 */
.category-nav {
  padding: 32rpx;
  white-space: nowrap;
}

.category-container {
  display: flex;
  gap: 24rpx;
}

.category-item {
  background: white;
  border: 2rpx solid #e5e5e5;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s;
}

.category-item.active {
  background: #0052d9;
  color: white;
  border-color: #0052d9;
}

/* 工具列表样式 */
.tools-container {
  padding: 0 32rpx 32rpx;
}

.tool-card {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e5e5;
  padding: 32rpx;
  transition: all 0.3s;
}

.tool-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.tool-disabled {
  opacity: 0.5;
}

.tool-content {
  display: flex;
  align-items: center;
}

.tool-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.tool-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tool-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.tool-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}

.tool-arrow {
  color: #ccc;
  font-size: 32rpx;
  margin-left: 16rpx;
}
