<!--relative-calculator.wxml-->
<scroll-view class="container" scroll-y>
  <!-- 头部 -->
  <view class="header">
    <view class="header-content">
      <text class="header-title">👨‍👩‍👧‍👦 亲戚关系计算器</text>
      <text class="header-subtitle">快速计算复杂的亲戚关系</text>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-card">
      <text class="section-title">构建关系链</text>
      
      <!-- 当前关系链 -->
      <view class="relation-chain">
        <view wx:if="{{relationChain.length === 0}}" class="chain-placeholder">
          点击下方按钮添加关系...
        </view>
        <block wx:else>
          <view wx:for="{{relationChain}}" wx:key="index" class="relation-item">
            <text class="relation-icon">{{item.gender === 'male' ? '👨' : '👩'}}</text>
            <text class="relation-text">{{item.relation}}</text>
            <text class="remove-btn" bindtap="removeRelation" data-index="{{index}}">×</text>
          </view>
          <text wx:if="{{index < relationChain.length - 1}}" class="relation-arrow">→</text>
        </block>
      </view>
      
      <!-- 快捷关系按钮 -->
      <view class="quick-buttons">
        <view 
          wx:for="{{quickRelations}}" 
          wx:key="relation"
          class="quick-btn"
          bindtap="addQuickRelation"
          data-relation="{{item.relation}}"
          data-gender="{{item.gender}}"
        >
          <text class="quick-icon">{{item.icon}}</text>
          <text class="quick-text">{{item.relation}}</text>
        </view>
      </view>
      
      <!-- 自定义输入 -->
      <view class="custom-input-section">
        <text class="input-label">自定义关系</text>
        <t-input 
          value="{{customRelation}}"
          placeholder="输入其他亲戚关系"
          bindchange="onCustomInputChange"
          class="custom-input"
        />
        <view class="gender-selector">
          <t-button 
            theme="{{selectedGender === 'male' ? 'primary' : 'default'}}"
            size="small"
            bindtap="selectGender"
            data-gender="male"
            class="gender-btn"
          >
            👨 男性
          </t-button>
          <t-button 
            theme="{{selectedGender === 'female' ? 'primary' : 'default'}}"
            size="small"
            bindtap="selectGender"
            data-gender="female"
            class="gender-btn"
          >
            👩 女性
          </t-button>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <t-button theme="default" size="medium" bindtap="addCustomRelation" class="action-btn">
          添加关系
        </t-button>
        <t-button theme="default" size="medium" bindtap="clearChain" class="action-btn">
          清空
        </t-button>
        <t-button theme="primary" size="medium" bindtap="calculateRelation" class="action-btn">
          计算称谓
        </t-button>
      </view>
    </view>
  </view>

  <!-- 结果展示 -->
  <view wx:if="{{showResult}}" class="result-section">
    <view class="result-card">
      <text class="result-header">🎯 计算结果</text>
      
      <view class="result-title">{{result.title}}</view>
      
      <view class="result-description">
        <text class="result-desc">{{result.description}}</text>
      </view>
      
      <!-- 关系图谱 -->
      <view class="family-tree">
        <text class="tree-title">关系图谱</text>
        <view class="tree-content">
          <view wx:for="{{familyTree.levels}}" wx:key="name" class="tree-level">
            <text class="level-name">{{item.name}}</text>
            <view class="level-members">
              <view 
                wx:for="{{item.members}}" 
                wx:for-item="member"
                wx:key="member"
                class="tree-node {{member === '我' ? 'highlight' : ''}}"
              >
                {{member}}
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 其他称谓 -->
      <view wx:if="{{result.otherNames.length > 0}}" class="other-names">
        <text class="other-names-title">📝 其他称谓</text>
        <view class="other-names-list">
          <text wx:for="{{result.otherNames}}" wx:key="index" class="other-name-item">
            • {{item}}
          </text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="result-actions">
        <t-button theme="default" size="medium" bindtap="shareResult" class="result-action-btn">
          分享结果
        </t-button>
        <t-button theme="primary" size="medium" bindtap="resetCalculator" class="result-action-btn">
          重新计算
        </t-button>
      </view>
    </view>
  </view>
</scroll-view>
