<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作性价比计算器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://tdesign.tencent.com/miniprogram/dist/tdesign.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
            padding: 44px 20px 20px;
            text-align: center;
        }
        
        .form-card {
            background: white;
            margin: 16px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e5e5;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #0052d9;
        }
        
        .input-suffix {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 14px;
        }
        
        .input-wrapper {
            position: relative;
        }
        
        .result-card {
            background: white;
            margin: 16px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#4ecdc4 0deg 252deg, #e5e5e5 252deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
        }
        
        .score-circle::before {
            content: '';
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            position: absolute;
        }
        
        .score-text {
            position: relative;
            z-index: 1;
            font-size: 24px;
            font-weight: bold;
            color: #4ecdc4;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-size: 14px;
            color: #666;
        }
        
        .metric-value {
            font-weight: 600;
            color: #333;
        }
        
        .tab-container {
            display: flex;
            background: #f0f0f0;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 20px;
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .tab-item.active {
            background: white;
            color: #0052d9;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <h1 class="text-xl font-bold">💰 工作性价比计算器</h1>
        <p class="text-sm opacity-90 mt-2">科学评估你的工作价值</p>
    </div>
    
    <!-- 标签页 -->
    <div class="form-card">
        <div class="tab-container">
            <div class="tab-item active" onclick="switchTab('input')">信息输入</div>
            <div class="tab-item" onclick="switchTab('result')">计算结果</div>
        </div>
        
        <!-- 输入表单 -->
        <div id="inputTab">
            <form id="workForm">
                <div class="input-group">
                    <label class="input-label">年薪（税前）</label>
                    <div class="input-wrapper">
                        <input type="number" class="input-field" id="annualSalary" placeholder="请输入年薪">
                        <span class="input-suffix">万元</span>
                    </div>
                </div>
                
                <div class="input-group">
                    <label class="input-label">每周工作天数</label>
                    <div class="input-wrapper">
                        <input type="number" class="input-field" id="workDaysPerWeek" placeholder="5" value="5">
                        <span class="input-suffix">天</span>
                    </div>
                </div>
                
                <div class="input-group">
                    <label class="input-label">每日工作小时</label>
                    <div class="input-wrapper">
                        <input type="number" class="input-field" id="workHoursPerDay" placeholder="8" value="8">
                        <span class="input-suffix">小时</span>
                    </div>
                </div>
                
                <div class="input-group">
                    <label class="input-label">年假天数</label>
                    <div class="input-wrapper">
                        <input type="number" class="input-field" id="vacationDays" placeholder="10" value="10">
                        <span class="input-suffix">天</span>
                    </div>
                </div>
                
                <div class="input-group">
                    <label class="input-label">通勤时间（单程）</label>
                    <div class="input-wrapper">
                        <input type="number" class="input-field" id="commuteTime" placeholder="30" value="30">
                        <span class="input-suffix">分钟</span>
                    </div>
                </div>
                
                <div class="input-group">
                    <label class="input-label">工作满意度</label>
                    <div class="input-wrapper">
                        <input type="range" class="w-full" id="satisfaction" min="1" max="10" value="7">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>1分</span>
                            <span id="satisfactionValue">7分</span>
                            <span>10分</span>
                        </div>
                    </div>
                </div>
                
                <button type="button" onclick="calculateWorth()" class="w-full bg-teal-500 text-white py-3 rounded-lg font-medium mt-6">
                    计算性价比
                </button>
            </form>
        </div>
        
        <!-- 结果展示 -->
        <div id="resultTab" style="display: none;">
            <div class="text-center mb-6">
                <div class="score-circle">
                    <div class="score-text" id="totalScore">75</div>
                </div>
                <h3 class="text-lg font-bold text-gray-800">综合性价比评分</h3>
                <p class="text-sm text-gray-600 mt-1">你的工作性价比较高</p>
            </div>
            
            <div class="space-y-0">
                <div class="metric-item">
                    <span class="metric-label">年工作天数</span>
                    <span class="metric-value" id="workDaysPerYear">250 天</span>
                </div>
                
                <div class="metric-item">
                    <span class="metric-label">平均日薪</span>
                    <span class="metric-value" id="dailySalary">¥400</span>
                </div>
                
                <div class="metric-item">
                    <span class="metric-label">平均时薪</span>
                    <span class="metric-value" id="hourlySalary">¥50</span>
                </div>
                
                <div class="metric-item">
                    <span class="metric-label">有效工作时间</span>
                    <span class="metric-value" id="effectiveHours">8 小时/天</span>
                </div>
                
                <div class="metric-item">
                    <span class="metric-label">通勤成本</span>
                    <span class="metric-value" id="commuteCost">1 小时/天</span>
                </div>
                
                <div class="metric-item">
                    <span class="metric-label">满意度指数</span>
                    <span class="metric-value" id="satisfactionIndex">7.0/10</span>
                </div>
            </div>
            
            <div class="mt-6 p-4 bg-teal-50 rounded-lg">
                <h4 class="font-semibold mb-2 text-teal-800">💡 优化建议</h4>
                <ul class="text-sm text-teal-700 space-y-1">
                    <li>• 考虑协商远程工作减少通勤时间</li>
                    <li>• 提升技能争取更高薪资</li>
                    <li>• 寻找工作与生活平衡点</li>
                </ul>
            </div>
            
            <div class="flex gap-3 mt-6">
                <button class="flex-1 py-2 border border-gray-300 rounded-lg text-sm">
                    分享结果
                </button>
                <button class="flex-1 py-2 bg-teal-500 text-white rounded-lg text-sm">
                    保存报告
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 满意度滑块更新
        document.getElementById('satisfaction').addEventListener('input', function() {
            document.getElementById('satisfactionValue').textContent = this.value + '分';
        });
        
        function switchTab(tab) {
            // 更新标签页样式
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 切换内容
            if (tab === 'input') {
                document.getElementById('inputTab').style.display = 'block';
                document.getElementById('resultTab').style.display = 'none';
            } else {
                document.getElementById('inputTab').style.display = 'none';
                document.getElementById('resultTab').style.display = 'block';
            }
        }
        
        function calculateWorth() {
            // 获取输入值
            const annualSalary = parseFloat(document.getElementById('annualSalary').value) || 0;
            const workDaysPerWeek = parseFloat(document.getElementById('workDaysPerWeek').value) || 5;
            const workHoursPerDay = parseFloat(document.getElementById('workHoursPerDay').value) || 8;
            const vacationDays = parseFloat(document.getElementById('vacationDays').value) || 10;
            const commuteTime = parseFloat(document.getElementById('commuteTime').value) || 30;
            const satisfaction = parseFloat(document.getElementById('satisfaction').value) || 7;
            
            // 计算工作天数
            const workDaysPerYear = (workDaysPerWeek * 52) - vacationDays;
            
            // 计算薪资
            const dailySalary = (annualSalary * 10000) / workDaysPerYear;
            const hourlySalary = dailySalary / workHoursPerDay;
            
            // 计算综合评分 (简化算法)
            const salaryScore = Math.min((hourlySalary / 100) * 30, 30); // 时薪评分 (最高30分)
            const timeScore = Math.max(30 - (commuteTime / 60) * 10, 10); // 通勤时间评分 (最高30分)
            const satisfactionScore = satisfaction * 4; // 满意度评分 (最高40分)
            
            const totalScore = Math.round(salaryScore + timeScore + satisfactionScore);
            
            // 更新结果显示
            document.getElementById('totalScore').textContent = totalScore;
            document.getElementById('workDaysPerYear').textContent = Math.round(workDaysPerYear) + ' 天';
            document.getElementById('dailySalary').textContent = '¥' + Math.round(dailySalary);
            document.getElementById('hourlySalary').textContent = '¥' + Math.round(hourlySalary);
            document.getElementById('effectiveHours').textContent = workHoursPerDay + ' 小时/天';
            document.getElementById('commuteCost').textContent = (commuteTime * 2 / 60).toFixed(1) + ' 小时/天';
            document.getElementById('satisfactionIndex').textContent = satisfaction + '.0/10';
            
            // 更新圆形进度条
            const percentage = (totalScore / 100) * 360;
            document.querySelector('.score-circle').style.background = 
                `conic-gradient(#4ecdc4 0deg ${percentage}deg, #e5e5e5 ${percentage}deg 360deg)`;
            
            // 切换到结果页
            switchTab('result');
            document.querySelectorAll('.tab-item')[1].classList.add('active');
            document.querySelectorAll('.tab-item')[0].classList.remove('active');
        }
    </script>
</body>
</html>
