/* mbti.wxss */
.container {
  height: 100vh;
  background: #f5f5f5;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40rpx;
  text-align: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 32rpx;
}

.progress-bar {
  width: 100%;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 介绍页面样式 */
.intro-section {
  padding: 32rpx;
}

.intro-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e5e5;
}

.intro-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 32rpx;
  display: block;
}

.intro-content {
  margin-bottom: 48rpx;
}

.intro-item {
  margin-bottom: 32rpx;
}

.intro-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.intro-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: block;
}

.dimension-list,
.instruction-list {
  margin-top: 16rpx;
}

.dimension-item,
.instruction-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
  display: block;
}

.start-btn {
  width: 100%;
}

/* 测试页面样式 */
.test-section {
  padding: 32rpx;
}

.question-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e5e5;
}

.question-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  display: block;
  line-height: 1.4;
}

.options-container {
  margin-bottom: 48rpx;
}

.option-item {
  margin-bottom: 16rpx;
}

.button-container {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}

.nav-btn {
  flex: 1;
}

/* 结果页面样式 */
.result-section {
  padding: 32rpx;
}

.result-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e5e5;
  text-align: center;
}

.result-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  display: block;
}

.personality-type {
  font-size: 96rpx;
  font-weight: bold;
  color: #0052d9;
  margin-bottom: 16rpx;
  display: block;
}

.personality-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.personality-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 48rpx;
  display: block;
}

/* 得分区域样式 */
.scores-section {
  text-align: left;
  margin-bottom: 32rpx;
}

.scores-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  display: block;
}

.score-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 16rpx;
}

.score-label {
  font-size: 28rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.score-progress {
  flex: 1;
}

.score-value {
  font-size: 28rpx;
  color: #666;
  width: 80rpx;
  text-align: right;
  flex-shrink: 0;
}

/* 职业推荐样式 */
.careers-section {
  background: #f0f7ff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
  text-align: left;
}

.careers-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #0052d9;
  margin-bottom: 16rpx;
  display: block;
}

.careers-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  display: block;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
}
