# 青工具 - 微信小程序

一款集合多种实用工具的微信小程序，提供简便的操作与快速的功能体验，帮助用户进行个性化的测试、计算与分析。

## 功能特性

### 🏠 首页
- **Banner轮播区域**：支持多个Banner轮播展示，可通过配置文件管理
- **工具分类导航**：支持横向滑动的分类导航
- **工具列表**：按分类展示不同类型的工具

#### Banner配置功能
- **多Banner支持**：支持配置多个Banner进行轮播展示
- **自动轮播**：4秒自动切换，支持手动滑动
- **自定义配置**：可配置名称、描述、跳转路径、背景色、图标
- **启用/禁用**：可单独控制每个Banner的显示状态
- **排序管理**：支持自定义Banner显示顺序

### 🧠 MBTI性格测试
- **测试介绍**：详细的MBTI测试说明和四个维度介绍
- **20道测试题**：科学的题目设计，涵盖E/I、S/N、T/F、J/P四个维度
- **结果分析**：16种人格类型的详细分析和职业推荐
- **维度得分**：可视化的各维度得分展示

### 💰 工作性价比计算器
- **多维度输入**：年薪、工作天数、工作时间、通勤时间、满意度等
- **科学计算**：基于多个维度的综合评分算法
- **结果展示**：直观的评分圆环和详细的数据分析
- **优化建议**：个性化的工作改进建议

### 👨‍👩‍👧‍👦 亲戚关系计算器
- **关系链构建**：通过点击快捷按钮或自定义输入构建关系链
- **智能计算**：自动计算复杂的亲戚关系称谓
- **关系图谱**：可视化的家族关系图展示
- **多种称谓**：提供正式称谓和地方称谓

## 技术架构

### 前端框架
- **微信小程序**：使用原生小程序框架开发
- **TypeScript**：全面使用TypeScript提供类型安全
- **TDesign组件库**：使用腾讯TDesign小程序组件库

### 设计规范
- **统一样式**：参考原型设计，统一卡片圆角、阴影、间距等视觉元素
- **响应式布局**：适配不同尺寸的手机屏幕
- **交互反馈**：按钮点击、页面切换等交互效果
- **颜色系统**：统一的主色调和辅助色彩搭配

### 项目结构
```
miniprogram/
├── pages/                    # 页面目录
│   ├── index/               # 首页
│   ├── mbti/                # MBTI测试页面
│   ├── work-calculator/     # 工作性价比计算器
│   └── relative-calculator/ # 亲戚关系计算器
├── utils/                   # 工具函数
│   ├── tools-config.ts      # 工具配置
│   ├── mbti-questions.ts    # MBTI题库和算法
│   ├── work-calculator-config.ts # 工作计算器配置
│   ├── relative-data.ts     # 亲戚关系数据
│   └── test-config.ts       # 测试配置
├── assets/                  # 静态资源
├── app.json                 # 小程序配置
├── app.ts                   # 小程序入口
└── app.wxss                 # 全局样式
```

### 核心组件使用
- `t-button`: 按钮组件
- `t-cell`: 单元格组件
- `t-input`: 输入框组件
- `t-radio`: 单选框组件
- `t-progress`: 进度条组件
- `t-tabs`: 标签页组件
- `t-slider`: 滑块组件

### 样式指南

#### 卡片设计
- **圆角**：统一使用 24rpx 圆角（大卡片）和 12rpx 圆角（小卡片）
- **阴影**：`box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1)`
- **边框**：`border: 2rpx solid #e5e5e5`
- **间距**：卡片间距 16rpx，内边距 48rpx（大卡片）或 32rpx（小卡片）

#### 颜色规范
- **主色调**：`#0052d9` (蓝色)
- **背景色**：`#f5f5f5` (浅灰)
- **卡片背景**：`#ffffff` (白色)
- **主文本**：`#333333` (深灰)
- **辅助文本**：`#666666` (中灰)
- **提示文本**：`#999999` (浅灰)

#### 字体规范
- **大标题**：40rpx, font-weight: bold
- **中标题**：36rpx, font-weight: 600
- **小标题**：32rpx, font-weight: 600
- **正文**：28rpx, line-height: 1.6
- **辅助文本**：24rpx, line-height: 1.4

#### 间距规范
- **小间距**：16rpx
- **中间距**：32rpx
- **大间距**：48rpx

## 开发指南

### 环境要求
- 微信开发者工具
- Node.js 14+
- TypeScript 支持

### 安装依赖
```bash
npm install
```

### 开发调试
1. 使用微信开发者工具打开项目
2. 编译npm包：工具 -> 构建npm
3. 预览调试

### 配置说明

#### Banner配置 (`utils/banner-config.ts`)
- 添加新Banner：配置名称、描述、跳转路径
- 设置背景渐变色和图标
- 控制Banner启用状态和显示顺序
- 使用预设的背景色和图标

#### 工具配置 (`utils/tools-config.ts`)
- 添加新工具类型
- 修改工具分类
- 配置推荐工具

#### MBTI配置 (`utils/mbti-questions.ts`)
- 添加测试题目
- 修改计算算法
- 配置人格类型结果

#### 工作计算器配置 (`utils/work-calculator-config.ts`)
- 调整计算公式
- 修改评分权重
- 配置优化建议

#### 亲戚关系配置 (`utils/relative-data.ts`)
- 添加关系映射
- 配置快捷关系
- 扩展关系计算规则

## 部署发布

### 小程序发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核
4. 审核通过后发布

### 版本管理
- 使用语义化版本号
- 记录版本更新日志
- 保持向后兼容性

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 微信群讨论
