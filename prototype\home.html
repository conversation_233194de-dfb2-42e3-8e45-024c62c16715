<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>青工具 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://tdesign.tencent.com/miniprogram/dist/tdesign.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .banner-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            color: white;
            padding: 20px;
            margin: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .tool-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin: 8px 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e5e5;
        }
        
        .category-nav {
            display: flex;
            overflow-x: auto;
            padding: 16px;
            gap: 12px;
        }
        
        .category-item {
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 20px;
            padding: 8px 16px;
            white-space: nowrap;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .category-item.active {
            background: #0052d9;
            color: white;
            border-color: #0052d9;
        }
        
        .tool-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 12px;
        }
        
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <span>9:41</span>
        <span>青工具</span>
        <span>100%</span>
    </div>
    
    <!-- Banner推荐区域 -->
    <div class="banner-card">
        <h2 class="text-xl font-bold mb-2">🎯 推荐工具</h2>
        <h3 class="text-lg font-semibold mb-1">MBTI 性格测试</h3>
        <p class="text-sm opacity-90 mb-3">发现真实的自己，了解性格特质与职业匹配</p>
        <button class="bg-white text-purple-600 px-4 py-2 rounded-lg font-medium text-sm">
            立即测试 →
        </button>
    </div>
    
    <!-- 工具分类导航 -->
    <div class="category-nav">
        <div class="category-item active">全部</div>
        <div class="category-item">心理测试</div>
        <div class="category-item">计算工具</div>
        <div class="category-item">生活助手</div>
    </div>
    
    <!-- 工具列表 -->
    <div class="pb-4">
        <!-- 心理测试类 -->
        <div class="tool-card">
            <div class="flex items-center">
                <div class="tool-icon" style="background: linear-gradient(135deg, #ff6b6b, #ee5a24);">
                    🧠
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-800 mb-1">MBTI 性格测试</h3>
                    <p class="text-sm text-gray-600">专业的16型人格测试，了解你的性格类型</p>
                </div>
                <div class="text-gray-400">→</div>
            </div>
        </div>
        
        <!-- 计算工具类 -->
        <div class="tool-card">
            <div class="flex items-center">
                <div class="tool-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">
                    💰
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-800 mb-1">工作性价比计算器</h3>
                    <p class="text-sm text-gray-600">计算你的工作性价比，优化职业选择</p>
                </div>
                <div class="text-gray-400">→</div>
            </div>
        </div>
        
        <div class="tool-card">
            <div class="flex items-center">
                <div class="tool-icon" style="background: linear-gradient(135deg, #a8edea, #fed6e3);">
                    👨‍👩‍👧‍👦
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-800 mb-1">亲戚关系计算器</h3>
                    <p class="text-sm text-gray-600">快速计算复杂的亲戚关系称谓</p>
                </div>
                <div class="text-gray-400">→</div>
            </div>
        </div>
        
        <!-- 更多工具占位 -->
        <div class="tool-card opacity-50">
            <div class="flex items-center">
                <div class="tool-icon" style="background: linear-gradient(135deg, #ffeaa7, #fab1a0);">
                    🔧
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-800 mb-1">更多工具</h3>
                    <p class="text-sm text-gray-600">敬请期待更多实用工具...</p>
                </div>
                <div class="text-gray-400">→</div>
            </div>
        </div>
    </div>
</body>
</html>
