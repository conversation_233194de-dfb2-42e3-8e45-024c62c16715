<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>亲戚关系计算器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://tdesign.tencent.com/miniprogram/dist/tdesign.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            padding: 44px 20px 20px;
            text-align: center;
        }
        
        .input-card {
            background: white;
            margin: 16px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .relation-chain {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
            margin: 16px 0;
            min-height: 40px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px dashed #dee2e6;
        }
        
        .relation-item {
            background: #e3f2fd;
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .relation-arrow {
            color: #666;
            font-size: 16px;
        }
        
        .quick-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin: 16px 0;
        }
        
        .quick-btn {
            background: white;
            border: 2px solid #e5e5e5;
            border-radius: 8px;
            padding: 12px 8px;
            text-align: center;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .quick-btn:hover {
            border-color: #0052d9;
            background: #f0f7ff;
        }
        
        .result-card {
            background: white;
            margin: 16px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .result-title {
            font-size: 24px;
            font-weight: bold;
            color: #e91e63;
            text-align: center;
            margin: 16px 0;
        }
        
        .family-tree {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 16px 0;
            text-align: center;
        }
        
        .tree-node {
            background: white;
            border: 2px solid #e5e5e5;
            border-radius: 8px;
            padding: 8px 12px;
            margin: 4px;
            display: inline-block;
            font-size: 14px;
        }
        
        .tree-node.highlight {
            background: #e3f2fd;
            border-color: #1976d2;
            color: #1976d2;
            font-weight: 600;
        }
        
        .tree-level {
            margin: 12px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .tree-connector {
            width: 20px;
            height: 2px;
            background: #ccc;
            margin: 0 4px;
        }
        
        .input-group {
            margin-bottom: 16px;
        }
        
        .input-label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .custom-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e5e5;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .custom-input:focus {
            outline: none;
            border-color: #0052d9;
        }
        
        .gender-selector {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }
        
        .gender-btn {
            flex: 1;
            padding: 8px 16px;
            border: 2px solid #e5e5e5;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
            font-size: 14px;
        }
        
        .gender-btn.active {
            border-color: #0052d9;
            background: #0052d9;
            color: white;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <h1 class="text-xl font-bold">👨‍👩‍👧‍👦 亲戚关系计算器</h1>
        <p class="text-sm opacity-80 mt-2">快速计算复杂的亲戚关系</p>
    </div>
    
    <!-- 输入区域 -->
    <div class="input-card">
        <h3 class="font-semibold mb-4">构建关系链</h3>
        
        <!-- 当前关系链 -->
        <div class="relation-chain" id="relationChain">
            <span class="text-gray-500 text-sm">点击下方按钮添加关系...</span>
        </div>
        
        <!-- 快捷关系按钮 -->
        <div class="quick-buttons">
            <button class="quick-btn" onclick="addRelation('爸爸', 'male')">👨 爸爸</button>
            <button class="quick-btn" onclick="addRelation('妈妈', 'female')">👩 妈妈</button>
            <button class="quick-btn" onclick="addRelation('哥哥', 'male')">👦 哥哥</button>
            <button class="quick-btn" onclick="addRelation('姐姐', 'female')">👧 姐姐</button>
            <button class="quick-btn" onclick="addRelation('弟弟', 'male')">👦 弟弟</button>
            <button class="quick-btn" onclick="addRelation('妹妹', 'female')">👧 妹妹</button>
            <button class="quick-btn" onclick="addRelation('儿子', 'male')">👶 儿子</button>
            <button class="quick-btn" onclick="addRelation('女儿', 'female')">👶 女儿</button>
            <button class="quick-btn" onclick="addRelation('老公', 'male')">👨 老公</button>
            <button class="quick-btn" onclick="addRelation('老婆', 'female')">👩 老婆</button>
            <button class="quick-btn" onclick="addRelation('爷爷', 'male')">👴 爷爷</button>
            <button class="quick-btn" onclick="addRelation('奶奶', 'female')">👵 奶奶</button>
        </div>
        
        <!-- 自定义输入 -->
        <div class="input-group">
            <label class="input-label">自定义关系</label>
            <input type="text" class="custom-input" id="customRelation" placeholder="输入其他亲戚关系">
            <div class="gender-selector">
                <button class="gender-btn" onclick="setGender('male')" id="maleBtn">👨 男性</button>
                <button class="gender-btn" onclick="setGender('female')" id="femaleBtn">👩 女性</button>
            </div>
        </div>
        
        <div class="flex gap-3 mt-4">
            <button onclick="addCustomRelation()" class="flex-1 py-2 bg-blue-600 text-white rounded-lg text-sm">
                添加关系
            </button>
            <button onclick="clearChain()" class="px-4 py-2 border border-gray-300 rounded-lg text-sm">
                清空
            </button>
            <button onclick="calculateRelation()" class="flex-1 py-2 bg-pink-500 text-white rounded-lg text-sm">
                计算称谓
            </button>
        </div>
    </div>
    
    <!-- 结果展示 -->
    <div class="result-card" id="resultCard" style="display: none;">
        <h3 class="font-semibold mb-4 text-center">🎯 计算结果</h3>
        
        <div class="result-title" id="resultTitle">姑妈</div>
        
        <div class="text-center mb-4">
            <p class="text-sm text-gray-600" id="resultDescription">
                你爸爸的姐姐，应该称呼为"姑妈"或"姑姑"
            </p>
        </div>
        
        <!-- 关系图谱 -->
        <div class="family-tree">
            <h4 class="font-semibold mb-3">关系图谱</h4>
            
            <div class="tree-level">
                <div class="tree-node">爷爷奶奶</div>
            </div>
            
            <div class="tree-level">
                <div class="tree-node">姑妈</div>
                <div class="tree-connector"></div>
                <div class="tree-node highlight">爸爸</div>
            </div>
            
            <div class="tree-level">
                <div class="tree-node highlight">我</div>
            </div>
        </div>
        
        <!-- 其他称谓 -->
        <div class="mt-4 p-4 bg-pink-50 rounded-lg">
            <h4 class="font-semibold mb-2 text-pink-800">📝 其他称谓</h4>
            <div class="text-sm text-pink-700 space-y-1">
                <p>• 正式称谓：姑母</p>
                <p>• 常用称谓：姑妈、姑姑</p>
                <p>• 地方称谓：姑娘（部分地区）</p>
            </div>
        </div>
        
        <div class="flex gap-3 mt-6">
            <button class="flex-1 py-2 border border-gray-300 rounded-lg text-sm">
                分享结果
            </button>
            <button onclick="resetCalculator()" class="flex-1 py-2 bg-pink-500 text-white rounded-lg text-sm">
                重新计算
            </button>
        </div>
    </div>
    
    <script>
        let relationChain = [];
        let selectedGender = null;
        
        function addRelation(relation, gender) {
            relationChain.push({ relation, gender });
            updateRelationChain();
        }
        
        function addCustomRelation() {
            const customRelation = document.getElementById('customRelation').value.trim();
            if (!customRelation) {
                alert('请输入关系名称');
                return;
            }
            if (!selectedGender) {
                alert('请选择性别');
                return;
            }
            
            relationChain.push({ relation: customRelation, gender: selectedGender });
            updateRelationChain();
            
            // 清空输入
            document.getElementById('customRelation').value = '';
            selectedGender = null;
            document.querySelectorAll('.gender-btn').forEach(btn => btn.classList.remove('active'));
        }
        
        function setGender(gender) {
            selectedGender = gender;
            document.querySelectorAll('.gender-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(gender === 'male' ? 'maleBtn' : 'femaleBtn').classList.add('active');
        }
        
        function updateRelationChain() {
            const chainContainer = document.getElementById('relationChain');
            
            if (relationChain.length === 0) {
                chainContainer.innerHTML = '<span class="text-gray-500 text-sm">点击下方按钮添加关系...</span>';
                return;
            }
            
            let chainHTML = '';
            relationChain.forEach((item, index) => {
                if (index > 0) {
                    chainHTML += '<span class="relation-arrow">→</span>';
                }
                chainHTML += `<div class="relation-item">
                    ${item.gender === 'male' ? '👨' : '👩'} ${item.relation}
                    <button onclick="removeRelation(${index})" class="ml-1 text-red-500">×</button>
                </div>`;
            });
            
            chainContainer.innerHTML = chainHTML;
        }
        
        function removeRelation(index) {
            relationChain.splice(index, 1);
            updateRelationChain();
        }
        
        function clearChain() {
            relationChain = [];
            updateRelationChain();
        }
        
        function calculateRelation() {
            if (relationChain.length === 0) {
                alert('请先添加关系');
                return;
            }
            
            // 简化的计算逻辑（实际应用中需要更复杂的算法）
            let result = '未知关系';
            let description = '关系链过于复杂，暂时无法计算';
            
            // 简单的关系映射示例
            if (relationChain.length === 1) {
                result = relationChain[0].relation;
                description = `直接关系：${result}`;
            } else if (relationChain.length === 2) {
                const first = relationChain[0];
                const second = relationChain[1];
                
                if (first.relation === '爸爸' && second.relation === '姐姐') {
                    result = '姑妈';
                    description = '你爸爸的姐姐，应该称呼为"姑妈"或"姑姑"';
                } else if (first.relation === '妈妈' && second.relation === '哥哥') {
                    result = '舅舅';
                    description = '你妈妈的哥哥，应该称呼为"舅舅"';
                } else {
                    result = `${first.relation}的${second.relation}`;
                    description = `通过${first.relation}的关系链计算得出`;
                }
            }
            
            // 显示结果
            document.getElementById('resultTitle').textContent = result;
            document.getElementById('resultDescription').textContent = description;
            document.getElementById('resultCard').style.display = 'block';
            
            // 滚动到结果区域
            document.getElementById('resultCard').scrollIntoView({ behavior: 'smooth' });
        }
        
        function resetCalculator() {
            clearChain();
            document.getElementById('resultCard').style.display = 'none';
        }
    </script>
</body>
</html>
